import type { APIRoute } from 'astro';
import { createPolarClient } from '../../utils/polar';

export const prerender = false;

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    const { productId } = await request.json();
    
    if (!productId || !productId.trim()) {
      return new Response(
        JSON.stringify({ error: 'Product ID is required' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Get runtime environment from Cloudflare context
    const env = locals?.runtime?.env;
    const polar = createPolarClient(env);
    
    try {
      // Create checkout link with embed origin
      const checkoutLink = await polar.checkoutLinks.create({
        paymentProcessor: 'stripe',
        productId: productId.trim(),
        allowDiscountCodes: true,
        requireBillingAddress: false,
        successUrl: `${env?.PUBLIC_SITE_URL || import.meta.env.PUBLIC_SITE_URL}/success`,
        // Set embed origin for wallet payments support
        embedOrigin: env?.PUBLIC_SITE_URL || import.meta.env.PUBLIC_SITE_URL
      });

      return new Response(
        JSON.stringify({ 
          checkoutUrl: checkoutLink.url,
          checkoutId: checkoutLink.id 
        }),
        { 
          status: 200, 
          headers: { 'Content-Type': 'application/json' } 
        }
      );
      
    } catch (polarError) {
      console.error('Polar API error:', polarError);
      return new Response(
        JSON.stringify({ error: 'Failed to create checkout link' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }
    
  } catch (error) {
    console.error('Checkout URL API error:', error);
    return new Response(
      JSON.stringify({ error: 'Invalid request' }),
      { status: 400, headers: { 'Content-Type': 'application/json' } }
    );
  }
};
