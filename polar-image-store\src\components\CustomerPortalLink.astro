---
// Component để access customer portal
export interface Props {
  variant?: 'button' | 'link' | 'card';
  showEmailForm?: boolean;
  className?: string;
}

const { 
  variant = 'button', 
  showEmailForm = true,
  className = ''
} = Astro.props;
---

<div class={`customer-portal-section ${className}`}>
  {variant === 'card' && (
    <div class="bg-gradient-to-br from-primary-50 to-accent-50 border border-primary-200 rounded-xl p-6 text-center">
      <div class="w-12 h-12 bg-accent-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <svg class="w-6 h-6 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      </div>
      <h3 class="text-lg font-semibold text-primary-900 mb-2">Access Your Orders</h3>
      <p class="text-primary-700 mb-4">View your purchase history, downloads, and manage your account</p>

      <!-- Direct portal link -->
      <a
        href="/customer-portal"
        class="btn-primary mb-4"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        Go to Customer Portal
      </a>
      
      {showEmailForm && (
        <div class="border-t border-primary-200 pt-4">
          <p class="text-sm text-primary-600 mb-3">Or enter your email for quick access:</p>
          <form id="customerPortalQuickForm" class="flex gap-2">
            <input
              type="email"
              id="customerPortalQuickEmail"
              placeholder="<EMAIL>"
              class="flex-1 px-3 py-2 border border-primary-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 bg-white text-primary-900"
              required
            />
            <button
              type="submit"
              class="px-4 py-2 bg-accent-600 text-white rounded-lg text-sm font-medium hover:bg-accent-700 transition-colors"
            >
              Access
            </button>
          </form>
        </div>
      )}
    </div>
  )}

  {variant === 'button' && (
    <a
      href="/customer-portal"
      class="btn-secondary"
    >
      <img src="/cart.svg" alt="My Orders" class="w-5 h-5" />
      My Orders
    </a>
  )}

  {variant === 'link' && (
    <a
      href="/customer-portal"
      class="inline-flex items-center gap-2 text-accent-600 hover:text-accent-700 font-medium transition-colors"
    >
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 640" class="w-4 h-4"><!--!Font Awesome Free v7.0.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.--><path fill="#f66336" d="M24 48C10.7 48 0 58.7 0 72C0 85.3 10.7 96 24 96L69.3 96C73.2 96 76.5 98.8 77.2 102.6L129.3 388.9C135.5 423.1 165.3 448 200.1 448L456 448C469.3 448 480 437.3 480 424C480 410.7 469.3 400 456 400L200.1 400C188.5 400 178.6 391.7 176.5 380.3L171.4 352L475 352C505.8 352 532.2 330.1 537.9 299.8L568.9 133.9C572.6 114.2 557.5 96 537.4 96L124.7 96L124.3 94C119.5 67.4 96.3 48 69.2 48L24 48zM208 576C234.5 576 256 554.5 256 528C256 501.5 234.5 480 208 480C181.5 480 160 501.5 160 528C160 554.5 181.5 576 208 576zM432 576C458.5 576 480 554.5 480 528C480 501.5 458.5 480 432 480C405.5 480 384 501.5 384 528C384 554.5 405.5 576 432 576z"/></svg>
      View Order History
    </a>
  )}
</div>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const quickForm = document.getElementById('customerPortalQuickForm');
    const quickEmail = document.getElementById('customerPortalQuickEmail');

    quickForm?.addEventListener('submit', (e) => {
      e.preventDefault();

      const email = quickEmail?.value.trim();
      if (email) {
        // Redirect to customer portal with email parameter
        const url = new URL('/customer-portal', window.location.origin);
        url.searchParams.set('email', email);
        window.location.href = url.toString();
      }
    });
  });
</script>
