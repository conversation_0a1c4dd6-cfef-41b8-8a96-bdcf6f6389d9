---
export interface Props {
  type: 'Product' | 'Organization' | 'WebSite' | 'BreadcrumbList' | 'FAQPage' | 'Article';
  data: any;
}

const { type, data } = Astro.props;

function generateStructuredData(type: string, data: any) {
  const baseData = {
    "@context": "https://schema.org",
    "@type": type
  };

  switch (type) {
    case 'Product':
      return {
        ...baseData,
        name: data.name,
        description: data.description,
        image: data.images || [],
        sku: data.id,
        brand: {
          "@type": "Brand",
          name: "InfPik"
        },
        offers: {
          "@type": "Offer",
          price: data.price,
          priceCurrency: data.currency || "USD",
          availability: data.isAvailable ? "https://schema.org/InStock" : "https://schema.org/OutOfStock",
          seller: {
            "@type": "Organization",
            name: "InfPik",
            url: "https://infpik.store"
          },
          url: data.url
        },
        category: "Digital Icons",
        productID: data.id,
        ...(data.aggregateRating && {
          aggregateRating: {
            "@type": "AggregateRating",
            ratingValue: data.aggregateRating.ratingValue,
            reviewCount: data.aggregateRating.reviewCount
          }
        })
      };

    case 'Organization':
      return {
        ...baseData,
        name: "InfPik",
        url: "https://infpik.store",
        logo: "https://infpik.store/favicon.svg",
        description: "Premium 3D Premium Icons Store",
        sameAs: [
          "https://twitter.com/polarimagestore",
          "https://facebook.com/polarimagestore"
        ],
        contactPoint: {
          "@type": "ContactPoint",
          contactType: "customer service",
          email: "<EMAIL>"
        }
      };

    case 'WebSite':
      return {
        ...baseData,
        name: "InfPik",
        url: "https://infpik.store",
        description: "3D Premium Icons Store",
        publisher: {
          "@type": "Organization",
          name: "InfPik"
        },
        potentialAction: {
          "@type": "SearchAction",
          target: "https://infpik.store/products?search={search_term_string}",
          "query-input": "required name=search_term_string"
        }
      };

    case 'BreadcrumbList':
      return {
        ...baseData,
        itemListElement: data.items.map((item: any, index: number) => ({
          "@type": "ListItem",
          position: index + 1,
          name: item.name,
          item: item.url
        }))
      };

    case 'FAQPage':
      return {
        ...baseData,
        mainEntity: data.faqs.map((faq: any) => ({
          "@type": "Question",
          name: faq.question,
          acceptedAnswer: {
            "@type": "Answer",
            text: faq.answer
          }
        }))
      };

    case 'Article':
      return {
        ...baseData,
        headline: data.title,
        author: {
          "@type": "Person",
          name: data.author
        },
        datePublished: data.publishDate,
        dateModified: data.modifiedDate,
        publisher: {
          "@type": "Organization",
          name: "InfPik"
        },
        image: data.images?.map((img: string) => ({
          "@type": "ImageObject",
          url: img,
          width: "1200",
          height: "900",
          caption: data.title
        })) || []
      };

    default:
      return baseData;
  }
}

const structuredData = generateStructuredData(type, data);
---

<script type="application/ld+json" set:html={JSON.stringify(structuredData)} />
