---
import Layout from '../layouts/Layout.astro';
---

<Layout title="Customer Portal - InfPik" description="Access your orders, downloads, and account information" noindex={true}>
  <section class="py-8 min-h-[60vh]">
    <div class="container max-w-4xl">
      <!-- Header -->
      <div class="text-center mb-8">
        <div class="w-16 h-16 bg-gradient-to-br from-accent-500 to-accent-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
        </div>
        <h1 class="text-4xl font-bold text-primary-900 mb-4">Customer Portal</h1>
        <p class="text-xl text-primary-600 leading-relaxed">
          Access your purchase history, downloads, and manage your account
        </p>
      </div>

      <!-- Email Form -->
      <div id="emailForm" class="bg-white rounded-2xl shadow-xl border border-primary-100 p-8 mb-8">
        <div class="text-center mb-6">
          <h2 class="text-2xl font-semibold text-primary-900 mb-2">Enter Your Email</h2>
          <p class="text-primary-600">We'll find your orders and account information</p>
        </div>
        
        <form id="customerPortalForm" class="max-w-md mx-auto">
          <div class="mb-4">
            <label for="customerEmail" class="block text-sm font-medium text-primary-700 mb-2">
              Email Address
            </label>
            <input
              type="email"
              id="customerEmail"
              name="email"
              required
              class="w-full px-4 py-3 border border-primary-200 rounded-xl bg-primary-50/50 text-primary-900 placeholder-primary-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200"
              placeholder="<EMAIL>"
            />
          </div>
          
          <button
            type="submit"
            class="btn-primary w-full"
            id="submitButton"
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 640" class="w-5 h-5"><!--!Font Awesome Free v7.0.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.--><path fill="#ffffff" d="M310.6 194.3L243.4 222.5L243.4 107.2L188.7 297.5L243.4 273.3L243.4 403.6L310.6 194.3zM227.4 97.6L226.1 102.3L210.9 155.2C170.6 170.7 142 209.8 142 255.5C142 307.8 176.3 351.4 225.4 361L225.4 414.6C147.5 404.1 90 336.4 90 255.6C90 175.1 149.8 108.4 227.4 97.6zM538.8 544.8C527.6 556 515.7 557.1 510.2 555.3C504.8 553.5 483.1 535.4 449.8 510.9C416.5 486.3 416.2 475.2 406.8 454.2C397.4 433.3 376.4 411.6 349.3 401.8L339.6 387.1C314.9 404 286.6 414 258.3 415.8L260.4 409.2L276.3 359.7C322.8 347.8 357.2 305.7 357.2 255.5C357.2 201 318.8 153.4 261.2 148.4L261.2 96.3C344.4 101.4 410 170.8 410 255.6C410 289.2 398.8 320.3 381 346L395.6 355.6C405.4 382.7 427.1 403.6 448 413C468.9 422.4 480.2 422.7 504.8 456C529.4 489.2 547.5 510.9 549.3 516.3C551.1 521.7 550 533.6 538.8 544.8zM528.9 526.9C528.9 522.5 525.3 518.9 520.9 518.9C516.5 518.9 512.9 522.5 512.9 526.9C512.9 531.3 516.5 534.9 520.9 534.9C525.3 534.9 528.9 531.3 528.9 526.9z"/></svg>
            Find My Orders
          </button>
        </form>

        <!-- Loading State -->
        <div id="loadingState" class="hidden text-center py-8">
          <div class="flex flex-col items-center justify-center gap-4 mb-4">
            <img src="/logo.svg" alt="InfPik" class="w-12 h-12 animate-pulse" />
            <span class="text-primary-600 font-medium">Searching for your orders...</span>
          </div>
        </div>

        <!-- Error State -->
        <div id="errorState" class="hidden bg-red-50 border border-red-200 rounded-xl p-4 mt-4">
          <div class="flex items-start gap-3">
            <svg class="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div class="flex-1">
              <p class="text-red-800 font-medium">Unable to find your account</p>
              <p class="text-red-600 text-sm" id="errorMessage">Please check your email address and try again.</p>
              <div class="mt-3">
                <button id="retryButton" class="text-red-600 hover:text-red-700 text-sm font-medium underline">
                  Try Again
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Customer Data Display -->
      <div id="customerData" class="hidden">
        <!-- Customer Info -->
        <div class="bg-white rounded-2xl shadow-xl border border-primary-100 p-8 mb-8">
          <div class="flex items-center justify-between mb-6">
            <div>
              <h2 class="text-2xl font-semibold text-primary-900">Account Information</h2>
              <p class="text-primary-600" id="customerEmail"></p>
            </div>
            <button
              id="backButton"
              class="btn-secondary"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Search Again
            </button>
          </div>
          
          <div class="grid md:grid-cols-3 gap-6">
            <div class="text-center p-4 bg-primary-50 rounded-xl">
              <div class="text-2xl font-bold text-accent-600" id="totalOrders">0</div>
              <div class="text-primary-600 text-sm">Total Orders</div>
            </div>
            <div class="text-center p-4 bg-primary-50 rounded-xl">
              <div class="text-2xl font-bold text-accent-600" id="totalSpent">$0</div>
              <div class="text-primary-600 text-sm">Total Spent</div>
            </div>
            <div class="text-center p-4 bg-primary-50 rounded-xl">
              <div class="text-2xl font-bold text-accent-600" id="memberSince">-</div>
              <div class="text-primary-600 text-sm">Member Since</div>
            </div>
          </div>
        </div>

        <!-- Orders List -->
        <div class="bg-white rounded-2xl shadow-xl border border-primary-100 p-8">
          <h3 class="text-xl font-semibold text-primary-900 mb-6">Order History</h3>
          <div id="ordersList">
            <!-- Orders will be populated here -->
          </div>

          <!-- Downloadables -->
          <div class="mt-10">
            <h4 class="text-lg font-semibold text-primary-900 mb-4">Your Downloads</h4>
            <div id="downloadsList" class="space-y-3"></div>
            <div id="emptyDownloads" class="hidden text-primary-600 text-sm">No downloadable files yet.</div>
          </div>

          <!-- Empty State -->
          <div id="emptyOrders" class="hidden text-center py-12">
            <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
              </svg>
            </div>
            <h4 class="text-lg font-medium text-primary-900 mb-2">No Orders Found</h4>
            <p class="text-primary-600 mb-6">You haven't made any purchases yet. Start exploring our collection!</p>
            <div class="flex flex-col sm:flex-row gap-3 justify-center">
              <a href="/products" class="btn-primary">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                Browse Our Collection
              </a>
              <a href="/trending" class="btn-secondary">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
                View Trending
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</Layout>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const form = document.getElementById('customerPortalForm');
    const emailForm = document.getElementById('emailForm');
    const customerData = document.getElementById('customerData');
    const loadingState = document.getElementById('loadingState');
    const errorState = document.getElementById('errorState');
    const backButton = document.getElementById('backButton');
    const submitButton = document.getElementById('submitButton');
    const emailInput = document.getElementById('customerEmail');

    // Check for email parameter in URL
    const urlParams = new URLSearchParams(window.location.search);
    const emailParam = urlParams.get('email');

    if (emailParam && emailInput) {
      emailInput.value = emailParam;
      // Auto-submit the form
      setTimeout(() => {
        form?.dispatchEvent(new Event('submit'));
      }, 100);
    }

    // Handle form submission
    form?.addEventListener('submit', async (e) => {
      e.preventDefault();

      const formData = new FormData(form);
      const email = formData.get('email')?.toString().trim();

      if (!email) {
        showError('Please enter your email address');
        return;
      }

      // Show loading state
      showLoading();

      try {
        const response = await fetch('/api/customer-orders', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email })
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Failed to fetch orders');
        }

        // Show customer data
        displayCustomerData(data);

      } catch (error) {
        console.error('Error fetching customer data:', error);
        showError(error.message || 'Unable to find your account. Please check your email and try again.');
      }
    });

    // Back button functionality
    backButton?.addEventListener('click', () => {
      resetForm();
    });

    // Retry button functionality
    const retryButton = document.getElementById('retryButton');
    retryButton?.addEventListener('click', () => {
      hideError();
    });

    function showLoading() {
      hideError();
      loadingState?.classList.remove('hidden');
      submitButton.disabled = true;
      submitButton.innerHTML = `
        <img src="/logo.svg" alt="InfPik" class="w-5 h-5 animate-pulse" />
        Searching...
      `;
    }

    function hideLoading() {
      loadingState?.classList.add('hidden');
      submitButton.disabled = false;
      submitButton.innerHTML = `
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0118 0z" />
        </svg>
        Find My Orders
      `;
    }

    function showError(message) {
      hideLoading();
      const errorMessage = document.getElementById('errorMessage');
      if (errorMessage) {
        errorMessage.textContent = message;
      }
      errorState?.classList.remove('hidden');
    }

    function hideError() {
      errorState?.classList.add('hidden');
    }

    function resetForm() {
      emailForm?.classList.remove('hidden');
      customerData?.classList.add('hidden');
      hideError();
      hideLoading();
      form?.reset();
    }

    function displayCustomerData(data) {
      hideLoading();
      emailForm?.classList.add('hidden');
      customerData?.classList.remove('hidden');

      // Update customer info
      const customerEmailEl = document.getElementById('customerEmail');
      if (customerEmailEl) {
        customerEmailEl.textContent = data.customer.email;
      }

      // Update stats
      const totalOrdersEl = document.getElementById('totalOrders');
      const totalSpentEl = document.getElementById('totalSpent');
      const memberSinceEl = document.getElementById('memberSince');

      if (totalOrdersEl) {
        totalOrdersEl.textContent = data.orders.length.toString();
      }

      if (totalSpentEl && data.orders.length > 0) {
        const totalSpent = data.orders.reduce((sum, order) => sum + (order.amount || 0), 0);
        totalSpentEl.textContent = formatCurrency(totalSpent, data.orders[0]?.currency || 'USD');
      }

      if (memberSinceEl && data.customer.createdAt) {
        const date = new Date(data.customer.createdAt);
        memberSinceEl.textContent = date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short'
        });
      }

      // Display orders
      displayOrders(data.orders);

      // Display downloads
      displayDownloads(data.benefits);
    }

    function displayOrders(orders) {
      const ordersList = document.getElementById('ordersList');
      const emptyOrders = document.getElementById('emptyOrders');

      if (!orders || orders.length === 0) {
        ordersList?.classList.add('hidden');
        emptyOrders?.classList.remove('hidden');
        return;
      }

      emptyOrders?.classList.add('hidden');
      ordersList?.classList.remove('hidden');

      if (ordersList) {
        ordersList.innerHTML = orders.map(order => createOrderCard(order)).join('');
      }
    }

    function displayDownloads(benefits) {
      const list = document.getElementById('downloadsList');
      const empty = document.getElementById('emptyDownloads');
      if (!list) return;
      if (!benefits || benefits.length === 0) {
        list.innerHTML = '';
        empty?.classList.remove('hidden');
        return;
      }
      empty?.classList.add('hidden');
      const rows = benefits.map(d => {
        const size = d?.file?.size ? `(${Math.round(d.file.size/1024)} KB)` : '';
        const name = d?.file?.name || 'File';
        const href = d?.file?.download?.url || d?.file?.url || '#';
        return `
          <div class="flex items-center justify-between border border-primary-200 rounded-xl p-4">
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v2a2 2 0 002 2h12a2 2 0 002-2v-2M7 10l5 5m0 0l5-5m-5 5V4"/></svg>
              </div>
              <div>
                <div class="font-medium text-primary-900">${name}</div>
                <div class="text-xs text-primary-600">${size}</div>
              </div>
            </div>
            <a href="${href}" class="btn-secondary text-sm" target="_blank" rel="noopener noreferrer">
              Download
            </a>
          </div>
        `;
      });
      list.innerHTML = rows.join('');
    }

    function createOrderCard(order) {
      const date = new Date(order.createdAt).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });

      const products = order.products || [];
      const productNames = products.map(p => p.name || 'Unknown Product').join(', ');
      const statusColor = getStatusColor(order.status);

      return `
        <div class="border border-primary-200 rounded-xl p-6 mb-4 hover:shadow-md transition-shadow">
          <div class="flex flex-col md:flex-row md:items-center justify-between gap-4">
            <div class="flex-1">
              <div class="flex items-center gap-3 mb-2">
                <div class="w-10 h-10 bg-accent-100 rounded-full flex items-center justify-center">
                  <svg class="w-5 h-5 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                  </svg>
                </div>
                <div>
                  <h4 class="font-semibold text-primary-900">Order #${order.id.slice(-8)}</h4>
                  <p class="text-sm text-primary-600">${date}</p>
                </div>
              </div>
              <p class="text-primary-700 mb-2">${productNames}</p>
              <div class="flex items-center gap-4">
                <span class="text-lg font-semibold text-primary-900">
                  ${formatCurrency(order.amount, order.currency)}
                </span>
                <span class="px-3 py-1 ${statusColor} rounded-full text-sm font-medium">
                  ${getStatusText(order.status)}
                </span>
              </div>
            </div>
            <div class="flex flex-col sm:flex-row gap-2">
              ${order.checkoutId ? `
                <a href="/success?checkout_id=${order.checkoutId}" class="btn-secondary text-sm">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  View Receipt
                </a>
              ` : ''}
              <button onclick="copyOrderId('${order.id}')" class="btn-secondary text-sm" title="Copy Order ID">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                Copy ID
              </button>
            </div>
          </div>
        </div>
      `;
    }

    function getStatusColor(status) {
      switch (status?.toLowerCase()) {
        case 'paid':
          return 'bg-success-100 text-success-700';
        case 'pending':
          return 'bg-warning-100 text-warning-700';
        case 'failed':
          return 'bg-red-100 text-red-700';
        case 'refunded':
          return 'bg-primary-100 text-primary-700';
        default:
          return 'bg-primary-100 text-primary-700';
      }
    }

    function getStatusText(status) {
      switch (status?.toLowerCase()) {
        case 'paid':
          return 'Paid';
        case 'pending':
          return 'Pending';
        case 'failed':
          return 'Failed';
        case 'refunded':
          return 'Refunded';
        default:
          return status || 'Unknown';
      }
    }

    // Global function for copying order ID
    window.copyOrderId = function(orderId) {
      navigator.clipboard.writeText(orderId).then(() => {
        // Show toast notification
        showToast('Order ID copied to clipboard!');
      }).catch(() => {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = orderId;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showToast('Order ID copied to clipboard!');
      });
    };

    function showToast(message) {
      // Create toast element
      const toast = document.createElement('div');
      toast.className = 'fixed top-4 right-4 bg-success-600 text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-all duration-300 transform translate-x-full';
      toast.textContent = message;

      document.body.appendChild(toast);

      // Animate in
      setTimeout(() => {
        toast.classList.remove('translate-x-full');
      }, 100);

      // Animate out and remove
      setTimeout(() => {
        toast.classList.add('translate-x-full');
        setTimeout(() => {
          document.body.removeChild(toast);
        }, 300);
      }, 3000);
    }

    function formatCurrency(amount, currency = 'USD') {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency.toUpperCase(),
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      }).format(amount / 100); // Convert from cents
    }
  });
</script>
