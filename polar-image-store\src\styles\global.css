@import "tailwindcss";

/* Search dropdown z-index fix */
#heroSearchResults {
  z-index: 9999 !important;
  position: absolute !important;
}

/* Font face declarations with performance optimizations */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap; /* Prevent invisible text during font load */
  src: url('/fonts/inter.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* Custom theme configuration for Tailwind CSS v4 */
@theme {
  /* Primary colors - Modern blue-gray */
  --color-primary-50: oklch(0.98 0.01 240);
  --color-primary-100: oklch(0.96 0.02 240);
  --color-primary-200: oklch(0.92 0.04 240);
  --color-primary-300: oklch(0.86 0.06 240);
  --color-primary-400: oklch(0.76 0.08 240);
  --color-primary-500: oklch(0.64 0.10 240);
  --color-primary-600: oklch(0.52 0.12 240);
  --color-primary-700: oklch(0.42 0.10 240);
  --color-primary-800: oklch(0.32 0.08 240);
  --color-primary-900: oklch(0.22 0.06 240);
  --color-primary-950: oklch(0.14 0.04 240);

  /* Accent colors - Orange (brighter) */
  --color-accent-50: oklch(0.98 0.02 37);
  --color-accent-100: oklch(0.95 0.05 37);
  --color-accent-200: oklch(0.90 0.10 37);
  --color-accent-300: oklch(0.86 0.14 37);
  --color-accent-400: oklch(0.82 0.18 37);
  --color-accent-500: oklch(0.75 0.2058 37);
  --color-accent-600: oklch(0.68 0.19 37);
  --color-accent-700: oklch(0.58 0.17 37);
  --color-accent-800: oklch(0.48 0.15 37);
  --color-accent-900: oklch(0.38 0.12 37);
  --color-accent-950: oklch(0.28 0.08 37);

  /* Success colors - Complementary orange */
  --color-success-50: oklch(0.98 0.02 40);
  --color-success-100: oklch(0.95 0.04 40);
  --color-success-200: oklch(0.90 0.08 40);
  --color-success-300: oklch(0.84 0.12 40);
  --color-success-400: oklch(0.78 0.16 40);
  --color-success-500: oklch(0.72 0.18 40);
  --color-success-600: oklch(0.65 0.17 40);
  --color-success-700: oklch(0.55 0.15 40);
  --color-success-800: oklch(0.45 0.13 40);
  --color-success-900: oklch(0.35 0.11 40);
  --color-success-950: oklch(0.25 0.07 40);

  /* Warning colors - Orange */
  --color-warning-50: oklch(0.98 0.02 60);
  --color-warning-100: oklch(0.95 0.05 60);
  --color-warning-200: oklch(0.90 0.10 60);
  --color-warning-300: oklch(0.82 0.15 60);
  --color-warning-400: oklch(0.72 0.20 60);
  --color-warning-500: oklch(0.62 0.25 60);
  --color-warning-600: oklch(0.52 0.22 60);
  --color-warning-700: oklch(0.42 0.18 60);
  --color-warning-800: oklch(0.32 0.14 60);
  --color-warning-900: oklch(0.22 0.10 60);
  --color-warning-950: oklch(0.12 0.06 60);

  /* Typography */
  --font-display: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-body: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-mono: "JetBrains Mono", "Fira Code", Consolas, monospace;

  /* Layout */
  --breakpoint-3xl: 1920px;
  --container-max-width: 1280px;
  --header-height: 80px;

  /* Animations */
  --ease-smooth: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --ease-spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* Custom utilities */
@utility container {
  margin-inline: auto;
  padding-inline: 1rem;
  max-width: var(--container-max-width);

  @media (min-width: 640px) {
    padding-inline: 1.5rem;
  }

  @media (min-width: 1024px) {
    padding-inline: 2rem;
  }
}

@utility btn-primary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  font-size: 0.875rem;
  line-height: 1.25rem;
  border-radius: 9999px;
  background-color: var(--color-accent-600);
  color: white;
  border: none;
  cursor: pointer;
  transition: all 0.2s var(--ease-smooth);
  text-decoration: none;

  &:hover {
    background-color: var(--color-accent-700);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
  }

  &:active {
    transform: translateY(0);
  }
}

@utility btn-secondary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  font-size: 0.875rem;
  line-height: 1.25rem;
  border-radius: 9999px;
  background-color: white;
  color: var(--color-primary-700);
  border: 1px solid var(--color-primary-200);
  cursor: pointer;
  transition: all 0.2s var(--ease-smooth);
  text-decoration: none;

  &:hover {
    background-color: var(--color-primary-50);
    border-color: var(--color-primary-300);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }

  &:active {
    transform: translateY(0);
  }
}

/* Base styles */
@layer base {
  * {
    box-sizing: border-box;
  }

  body {
    font-family: var(--font-body);
    line-height: 1.6;
    color: var(--color-primary-800);
    background-color: var(--color-primary-50);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-display);
    font-weight: 700;
    line-height: 1.2;
    color: var(--color-primary-900);
  }

  h1 {
    font-size: 3rem;
    font-weight: 800;
    line-height: 1.1;
  }

  h2 {
    font-size: 2.25rem;
    font-weight: 700;
  }

  h3 {
    font-size: 1.875rem;
    font-weight: 600;
  }

  a {
    color: var(--color-accent-600);
    text-decoration: none;
    transition: color 0.2s var(--ease-smooth);
  }

  a:hover {
    color: var(--color-accent-700);
  }

  .text-balance {
    text-wrap: balance;
  }
}

/* Enhanced typography for legal & info pages */
@layer components {
  .prose {
    max-width: 100%;
    color: var(--color-primary-800);
  }

  .prose h2 {
    margin-top: 2.5rem;
    margin-bottom: 1rem;
    color: var(--color-primary-900);
    font-weight: 700;
  }

  .prose h3 {
    margin-top: 2rem;
    margin-bottom: 0.75rem;
    color: var(--color-primary-800);
    font-weight: 600;
  }

  .prose p {
    margin-bottom: 1rem;
  }

  .prose ul {
    padding-left: 1.5rem;
    list-style-type: disc;
  }

  .prose ul li::marker {
    color: var(--color-accent-600);
  }
}
