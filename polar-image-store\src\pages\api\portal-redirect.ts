import type { APIRoute } from 'astro';
import { createPolarClient } from '../../utils/polar';

export const prerender = false;

export const GET: APIRoute = async ({ url, locals }) => {
  try {
    const email = url.searchParams.get('email');
    
    // Get runtime environment from Cloudflare context
    const env = locals?.runtime?.env;
    const polar = createPolarClient(env);
    const organizationId = env?.POLAR_ORGANIZATION_ID || import.meta.env.POLAR_ORGANIZATION_ID;
    
    if (!organizationId) {
      return new Response(
        JSON.stringify({ error: 'Organization ID not configured' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // If email is provided, try to create pre-authenticated link
    if (email && email.trim()) {
      try {
        // Normalize & find customer by exact email (case-insensitive)
        const emailNormalized = email.trim().toLowerCase();
        let customerItems: any[] = [];
        try {
          const byEmail = await polar.customers.list({ email: emailNormalized });
          customerItems = (byEmail as any)?.items ?? (byEmail as any)?.result?.items ?? [];
        } catch {}
        if (!customerItems?.length) {
          try {
            const byQuery = await polar.customers.list({ query: emailNormalized, organizationId });
            customerItems = (byQuery as any)?.items ?? (byQuery as any)?.result?.items ?? [];
          } catch {}
        }
        const customer = (customerItems || []).find((c: any) => (c?.email || '').toLowerCase() === emailNormalized) || null;

        if (customer?.id) {
          // Create pre-authenticated customer session
          const session = await polar.customerSessions.create({ 
            customerId: customer.id 
          });
          
          if (session.customerPortalUrl) {
            return Response.redirect(session.customerPortalUrl, 302);
          }
        }
      } catch (error) {
        console.error('Error creating customer session:', error);
        // Fall through to direct portal redirect
      }
    }

    // Fallback: Redirect to direct portal URL where customer can enter email
    // Try to get organization info to get the slug
    try {
      const organization = await polar.organizations.get({ id: organizationId });
      const orgSlug = organization.slug;

      if (orgSlug) {
        const directPortalUrl = `https://polar.sh/${orgSlug}/portal`;
        return Response.redirect(directPortalUrl, 302);
      }
    } catch (error) {
      console.error('Error fetching organization info:', error);
    }

    // Final fallback: Use a generic portal URL
    const directPortalUrl = `https://polar.sh/portal`;
    return Response.redirect(directPortalUrl, 302);
    
  } catch (error) {
    console.error('Portal redirect error:', error);
    return new Response(
      JSON.stringify({ error: 'Failed to redirect to customer portal' }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
};
