---
import Layout from '../layouts/Layout.astro';
import { Image } from 'astro:assets';
---

<Layout 
  title="Page Not Found - InfPik" 
  description="The page you're looking for doesn't exist. Browse our beautiful collection of digital icons instead."
  noindex={true}
>
  <div class="min-h-[60vh] flex items-center justify-center px-4">
    <div class="text-center max-w-2xl mx-auto">
      <!-- 404 Illustration -->
      <div class="mb-8">
        <div class="flex items-center justify-center gap-2">
          <div class="text-8xl md:text-9xl font-bold text-primary-100 select-none">
            4
          </div>
          <div class="flex items-center justify-center">
            <Image
              src="/logo.svg"
              alt="InfPik Logo"
              width={64}
              height={64}
              class="w-16 h-16 text-accent-600 opacity-80"
            />
          </div>
          <div class="text-8xl md:text-9xl font-bold text-primary-100 select-none">
            4
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <div class="mb-8">
        <h1 class="text-3xl md:text-4xl font-bold text-primary-900 mb-4">
          Oops! Page Not Found
        </h1>
        <p class="text-lg text-primary-600 mb-6">
          The page you're looking for seems to have wandered off into the digital void. 
          Don't worry though – we have plenty of beautiful icons waiting for you!
        </p>
      </div>

      <!-- Action Buttons -->
      <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
        <a href="/" class="btn-primary">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
          </svg>
          Go Home
        </a>
        <a href="/products" class="btn-secondary">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          Browse Icons
        </a>
        <a href="/trending" class="btn-secondary">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
          </svg>
          Trending Now
        </a>
      </div>

      <!-- Search Suggestion -->
      <div class="bg-primary-50 rounded-2xl p-6 border border-primary-100">
        <h3 class="text-lg font-semibold text-primary-900 mb-3">
          Looking for something specific?
        </h3>
        <p class="text-primary-600 mb-4">
          Try searching for icons by category, style, or keyword.
        </p>
        <div class="relative max-w-md mx-auto">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="w-5 h-5 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <input
            type="text"
            id="error-page-search"
            class="block w-full pl-10 pr-4 py-3 border border-primary-200 rounded-full bg-white text-primary-900 placeholder-primary-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200"
            placeholder="Search for icons..."
            autocomplete="off"
          />
          <!-- Search results dropdown (hidden by default) -->
          <div id="errorPageSearchResults" class="absolute top-full left-0 right-0 mt-1 bg-white border border-primary-200 rounded-xl shadow-lg z-50 hidden max-h-96 overflow-y-auto">
            <!-- Search results will be populated here -->
          </div>
        </div>
      </div>

      <!-- Popular Categories -->
      <div class="mt-8">
        <h3 class="text-lg font-semibold text-primary-900 mb-4">
          Popular Categories
        </h3>
        <div class="flex flex-wrap gap-2 justify-center">
          <a href="/products/category/nature" class="inline-flex items-center px-4 py-2 bg-white border border-primary-200 rounded-full text-sm font-medium text-primary-700 hover:bg-primary-50 hover:border-accent-300 transition-all duration-200">
            🌿 Nature
          </a>
          <a href="/products/category/abstract" class="inline-flex items-center px-4 py-2 bg-white border border-primary-200 rounded-full text-sm font-medium text-primary-700 hover:bg-primary-50 hover:border-accent-300 transition-all duration-200">
            🎨 Abstract
          </a>
          <a href="/products/category/technology" class="inline-flex items-center px-4 py-2 bg-white border border-primary-200 rounded-full text-sm font-medium text-primary-700 hover:bg-primary-50 hover:border-accent-300 transition-all duration-200">
            💻 Technology
          </a>
          <a href="/products/category/business" class="inline-flex items-center px-4 py-2 bg-white border border-primary-200 rounded-full text-sm font-medium text-primary-700 hover:bg-primary-50 hover:border-accent-300 transition-all duration-200">
            💼 Business
          </a>
          <a href="/products/category/lifestyle" class="inline-flex items-center px-4 py-2 bg-white border border-primary-200 rounded-full text-sm font-medium text-primary-700 hover:bg-primary-50 hover:border-accent-300 transition-all duration-200">
            ✨ Lifestyle
          </a>
        </div>
      </div>
    </div>
  </div>
</Layout>

<script>
  // Handle search functionality on 404 page
  document.addEventListener('DOMContentLoaded', () => {
    const searchInput = document.getElementById('error-page-search');
    const searchResults = document.getElementById('errorPageSearchResults');
    
    let searchTimeout: any;
    
    // Mobile detection function
    function isMobile() {
      return window.innerWidth < 768; // md breakpoint
    }
    
    // Handle mobile search click - open SearchModal
    function handleMobileSearchClick(input: HTMLInputElement) {
      if (isMobile()) {
        input.blur(); // Remove focus to prevent keyboard
        const currentValue = input.value.trim();
        (window as any).openSearchModal?.(currentValue);
      }
    }
    
    // Save recent search
    function saveRecentSearch(query: string) {
      try {
        const recent = JSON.parse(localStorage.getItem('recentSearches') || '[]');
        const filtered = recent.filter((item: any) => item.query !== query);
        filtered.unshift({ query, timestamp: Date.now() });
        localStorage.setItem('recentSearches', JSON.stringify(filtered.slice(0, 10)));
      } catch (error) {
        console.error('Failed to save recent search:', error);
      }
    }
    
    // Load and display recent searches
    function loadRecentSearches() {
      if (!searchResults) return;
      
      const recentSearches = JSON.parse(localStorage.getItem('recentSearches') || '[]');
      
      if (recentSearches.length === 0) {
        searchResults.classList.add('hidden');
        return;
      }
      
      searchResults.innerHTML = `
        <div class="border-b border-primary-100 bg-gray-50 px-3 py-2">
          <div class="flex items-center justify-between">
            <span class="text-xs text-primary-500 font-medium">Recent Searches</span>
            <button id="clearRecentSearches" class="text-xs text-primary-400 hover:text-primary-600 transition-colors">
              Clear
            </button>
          </div>
        </div>
        <div class="p-2">
           ${recentSearches.map((query: string) => `
             <div class="flex items-center justify-between p-2 hover:bg-primary-50 rounded-lg transition-colors group">
               <button class="recent-search-item flex-1 text-left text-primary-700 hover:text-primary-900 transition-colors" data-query="${query}">
                 <div class="flex items-center gap-2">
                   <svg class="w-4 h-4 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                     <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                   </svg>
                   <span class="text-sm">${query}</span>
                 </div>
               </button>
               <button class="delete-recent-search ml-2 p-1 text-primary-400 hover:text-red-500 transition-colors" data-query="${query}">
                 <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                   <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                 </svg>
               </button>
             </div>
           `).join('')}
        </div>
      `;
      
      searchResults.classList.remove('hidden');
      
      // Add event listeners for recent search items
      searchResults.querySelectorAll('.recent-search-item').forEach(item => {
        item.addEventListener('click', (e: any) => {
          const query = e.currentTarget.dataset.query;
          if (searchInput) {
            searchInput.value = query;
            performSearch(query);
          }
        });
      });
      
      // Add event listeners for delete buttons
       searchResults.querySelectorAll('.delete-recent-search').forEach(button => {
         button.addEventListener('click', (e: any) => {
           e.stopPropagation();
           const query = e.currentTarget.dataset.query;
           let recentSearches = JSON.parse(localStorage.getItem('recentSearches') || '[]');
           recentSearches = recentSearches.filter((search: string) => search !== query);
           localStorage.setItem('recentSearches', JSON.stringify(recentSearches));
           loadRecentSearches();
         });
       });
      
      // Add event listener for clear all
      const clearButton = searchResults.querySelector('#clearRecentSearches');
      clearButton?.addEventListener('click', () => {
        localStorage.removeItem('recentSearches');
        searchResults?.classList.add('hidden');
      });
    }
    
    // Perform search API call
     async function performSearch(query: string) {
       if (!searchResults) return;
       
       try {
         const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`);
         const data = await response.json();
       
       if (data.results && data.results.length > 0) {
         // Save successful search to recent searches
         saveRecentSearch(query);
         displaySearchResults(data.results, query);
       } else {
         displayNoResults(query);
       }
       } catch (error) {
         console.error('Search failed:', error);
         displaySearchError();
       }
     }
    
    // Display search results
    function displaySearchResults(tags: any[], query: string) {
      if (!searchResults) return;
      
      const resultsHtml = tags.slice(0, 5).map((tag: any) => `
        <button onclick="window.location.href='${tag.url}'" class="block w-full p-3 hover:bg-primary-50 rounded-lg transition-colors border-b border-primary-100 last:border-b-0 text-left">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 bg-accent-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
              </div>
              <div class="flex-1 min-w-0">
                <div class="text-primary-900 font-medium truncate">${tag.displayName}</div>
                <div class="text-primary-600 text-sm">${tag.count} ${tag.count === 1 ? 'product' : 'products'}</div>
              </div>
            </div>
            <svg class="w-4 h-4 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </div>
        </button>
      `).join('');
      
      searchResults.innerHTML = `
        <div class="p-2">
          <div class="text-xs text-primary-500 px-3 py-2 border-b border-primary-100">
            Search All icons for "${query}"
          </div>
          ${resultsHtml}
          ${tags.length > 5 ? `
            <div class="p-3 border-t border-primary-100">
              <div class="text-center text-primary-600 text-sm">
                Showing ${tags.length} tags
              </div>
            </div>
          ` : ''}
        </div>
      `;
      
      searchResults.classList.remove('hidden');
    }
    
    // Display no results
     function displayNoResults(query: string) {
       if (!searchResults) return;
       
       searchResults.innerHTML = `
         <div class="p-4 text-center text-primary-600">
           <div class="text-sm">No tags found for "${query}"</div>
           <a href="/products" class="text-accent-600 hover:text-accent-700 font-medium text-sm mt-2 inline-block">
             Browse all icons →
           </a>
         </div>
       `;
       
       searchResults.classList.remove('hidden');
     }
    
    // Display search error
    function displaySearchError() {
      if (!searchResults) return;
      
      searchResults.innerHTML = `
        <div class="p-4 text-center text-red-600">
          <div class="text-sm">Search failed. Please try again.</div>
        </div>
      `;
      
      searchResults.classList.remove('hidden');
    }
    
    if (searchInput) {
      // Handle focus for mobile redirect
      searchInput.addEventListener('focus', (e: any) => {
        if (isMobile()) {
          handleMobileSearchClick(e.target);
        } else if (!e.target.value.trim()) {
          loadRecentSearches();
        }
      });
      
      // Handle click for mobile redirect
      searchInput.addEventListener('click', (e: any) => {
        if (isMobile()) {
          handleMobileSearchClick(e.target);
        }
      });
      
      // Handle input for desktop search
      searchInput.addEventListener('input', (e: any) => {
        if (isMobile()) return; // Skip on mobile as we use SearchModal
        
        const query = e.target.value.trim();
        
        // Clear previous timeout
        if (searchTimeout) {
          clearTimeout(searchTimeout);
        }
        
        if (query.length >= 2) {
          // Debounce search
          searchTimeout = setTimeout(() => {
            performSearch(query);
          }, 300);
        } else {
          // Show recent searches when input is empty
          loadRecentSearches();
        }
      });
      
      // Handle enter key
      searchInput.addEventListener('keydown', (e: any) => {
        if (e.key === 'Enter') {
          e.preventDefault();
          const query = e.target.value.trim();
          if (query) {
            // Save to recent searches before navigating
            saveRecentSearch(query);
            if (isMobile()) {
              // Open search modal with query instead of redirecting
              (window as any).openSearchModal?.(query);
            } else {
              window.location.href = `/products?search=${encodeURIComponent(query)}`;
            }
          }
        }
      });
    }
    
    // Hide search results when clicking outside
    document.addEventListener('click', (e: any) => {
      if (searchResults && !searchInput?.contains(e.target as Node) && !searchResults.contains(e.target as Node)) {
        searchResults.classList.add('hidden');
      }
    });
  });
</script>

<style>
  /* Custom animations for 404 page */
  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }
  
  .text-8xl, .text-9xl {
    animation: float 3s ease-in-out infinite;
  }
  
  /* Hover effects for category links */
  .inline-flex:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
  }
</style>