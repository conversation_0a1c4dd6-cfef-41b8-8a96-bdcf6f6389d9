# Cache Troubleshooting Guide

## 🚨 **COMMON ISSUES & SOLUTIONS**

### **Issue 1: Vẫn cần mở tab ẩn danh để thấy changes**

**Symptoms:**
- Content cũ vẫn hiển thị sau khi update
- Phải clear cache hoặc mở tab ẩn danh
- F5 refresh không work

**Root Causes:**
1. **Service Worker cache cũ** (Most common)
2. **Browser cache aggressive**
3. **Cloudflare edge cache**
4. **ETag không được generate đúng**

**Solutions:**

#### **Step 1: Clear Service Worker Cache**
```javascript
// Open DevTools → Console
navigator.serviceWorker.getRegistrations().then(function(registrations) {
  for(let registration of registrations) {
    registration.unregister();
  }
  location.reload();
});
```

#### **Step 2: Force Hard Refresh**
- **Chrome/Edge**: `Ctrl + Shift + R`
- **Firefox**: `Ctrl + F5`
- **Safari**: `Cmd + Shift + R`

#### **Step 3: Clear All Browser Cache**
```javascript
// DevTools → Application → Storage → Clear site data
caches.keys().then(names => {
  names.forEach(name => caches.delete(name));
  location.reload();
});
```

### **Issue 2: Service Worker không update**

**Symptoms:**
- Old Service Worker version vẫn active
- Cache strategy không thay đổi
- Console shows old SW version

**Solutions:**

#### **Check Service Worker Status**
```javascript
// DevTools → Application → Service Workers
navigator.serviceWorker.controller?.scriptURL
// Should show: /sw.js with latest timestamp
```

#### **Force Service Worker Update**
```javascript
// Force update và activate
navigator.serviceWorker.getRegistration().then(reg => {
  if (reg) {
    reg.update();
    reg.addEventListener('updatefound', () => {
      reg.installing.addEventListener('statechange', () => {
        if (reg.installing.state === 'installed') {
          location.reload();
        }
      });
    });
  }
});
```

### **Issue 3: ETag validation không work**

**Symptoms:**
- Luôn nhận 200 thay vì 304
- Cache headers có nhưng không validate
- Network tab không show conditional requests

**Check ETag Headers:**
```bash
# Test homepage ETag
curl -I https://infpik.store/
# Expected headers:
# ETag: "abc123"
# Last-Modified: Wed, 08 Jan 2025 12:00:00 GMT
# Cache-Control: public, max-age=300, s-maxage=300, must-revalidate

# Test conditional request
curl -H "If-None-Match: \"abc123\"" https://infpik.store/
# Expected: 304 Not Modified
```

**Solutions:**
1. **Check middleware ETag generation**
2. **Verify conditional request handling**
3. **Test with different browsers**

### **Issue 4: Cloudflare Cache Rules conflict**

**Symptoms:**
- Cache headers bị override
- TTL không match middleware settings
- Edge cache behavior inconsistent

**Check Cloudflare Rules:**
1. **Dashboard → Rules → Cache Rules**
2. **Verify rule priority và conditions**
3. **Check "Respect origin headers" setting**

**Recommended Settings:**
```
Rule 4: Respect Origin Headers for Dynamic Content
When: NOT file extensions AND NOT /cdn-cgi/image/
Then: 
  - Edge TTL: Use cache-control header if present, bypass if not
  - Browser TTL: Use cache-control header if present, bypass if not
```

## 🔧 **DIAGNOSTIC TOOLS**

### **Browser DevTools Checklist**

#### **Network Tab:**
- [ ] Cache-Control headers present
- [ ] ETag headers present  
- [ ] 304 responses for unchanged content
- [ ] Service Worker intercepts requests

#### **Application Tab:**
- [ ] Service Worker active và updated
- [ ] Cache storage shows correct entries
- [ ] Storage clear works properly

#### **Console:**
```javascript
// Check Service Worker version
console.log('SW:', navigator.serviceWorker.controller?.scriptURL);

// Check cache entries
caches.keys().then(keys => console.log('Caches:', keys));

// Check specific cache
caches.open('infpik-dynamic-v2').then(cache => 
  cache.keys().then(keys => console.log('Dynamic cache:', keys.length))
);
```

### **Command Line Testing**

#### **Test Cache Headers:**
```bash
# Homepage
curl -I https://infpik.store/
# Product page
curl -I https://infpik.store/products/example
# API route
curl -I https://infpik.store/api/products
# Static asset
curl -I https://infpik.store/_astro/main.css
```

#### **Test Conditional Requests:**
```bash
# Get ETag first
ETAG=$(curl -I https://infpik.store/ | grep -i etag | cut -d' ' -f2)

# Test If-None-Match
curl -H "If-None-Match: $ETAG" https://infpik.store/
# Should return 304 if ETag matches
```

## 📊 **MONITORING & VERIFICATION**

### **Performance Metrics:**

#### **Cache Hit Ratios:**
- **Static assets**: >95%
- **Images**: >90%  
- **HTML pages**: >70%
- **API routes**: >50%

#### **Load Time Improvements:**
- **First visit**: Baseline
- **Repeat visit**: 70-80% faster
- **Static assets**: 95%+ from cache
- **Dynamic content**: Fresh với efficient validation

### **Health Check Script:**
```javascript
// Comprehensive cache health check
async function cacheHealthCheck() {
  const results = {
    serviceWorker: !!navigator.serviceWorker.controller,
    cacheAPI: 'caches' in window,
    cacheEntries: 0,
    etag: null
  };
  
  // Check cache entries
  const cacheNames = await caches.keys();
  results.cacheEntries = cacheNames.length;
  
  // Check ETag on homepage
  const response = await fetch('/', { method: 'HEAD' });
  results.etag = response.headers.get('etag');
  
  console.table(results);
  return results;
}

// Run health check
cacheHealthCheck();
```

## 🎯 **BEST PRACTICES**

### **For Developers:**
1. **Always test cache behavior** sau mỗi deployment
2. **Monitor Service Worker updates** trong production
3. **Use proper cache headers** cho từng content type
4. **Implement ETag validation** cho dynamic content

### **For Users:**
1. **Hard refresh** nếu thấy content cũ
2. **Clear browser data** nếu vẫn có vấn đề
3. **Disable cache** trong DevTools khi debugging
4. **Check Network tab** để verify cache behavior

### **For Production:**
1. **Monitor cache hit ratios** trong Cloudflare Analytics
2. **Set up alerts** cho cache performance
3. **Regular cache purging** khi có major updates
4. **Test cache behavior** trên multiple browsers
