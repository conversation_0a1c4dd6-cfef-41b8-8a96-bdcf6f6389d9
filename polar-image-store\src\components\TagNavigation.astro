---
export interface Tag {
  id: string;
  name: string;
  count?: number;
}

export interface Props {
  tags: Tag[];
  activeTag?: string;
  variant?: 'default' | 'hero';
}

const {
  tags,
  activeTag = 'all',
  variant = 'default'
} = Astro.props;
---

{variant === 'hero' ? (
  <!-- Hero variant - simple horizontal scroll like CategoryNavigation -->
  <div class="text-center">
    <!-- Scroll container -->
    <div class="overflow-x-auto scrollbar-hide" id="tagScroll">
      <div class="flex gap-2 pb-2 min-w-max justify-center">
        {tags.map((tag) => (
          <button
            class={`tag-tab flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all whitespace-nowrap border ${
              activeTag === tag.id
                ? 'bg-accent-500 text-white border-accent-500 shadow-md'
                : 'bg-white text-primary-900 border-primary-200 shadow-sm hover:bg-primary-50 hover:border-accent-500'
            }`}
            data-tag={tag.id}
          >
            {tag.name}
          </button>
        ))}
      </div>
    </div>
  </div>
) : (
  <!-- Default variant - full section with background -->
  <section class="py-6 bg-primary-50 border-b border-primary-200">
    <div class="container">
      <div class="mb-4">
        <h3 class="text-lg font-semibold text-primary-900 mb-2">Browse by Tags</h3>
        <p class="text-sm text-primary-600">Find specific content with detailed tags</p>
      </div>

      <!-- Tag Navigation -->
      <!-- Scroll container -->
      <div class="overflow-x-auto scrollbar-hide" id="tagScroll">
        <div class="flex gap-2 pb-2 min-w-max">
          {tags.map((tag) => (
            <button
              class={`tag-tab flex items-center gap-2 px-3 py-1.5 rounded-full text-xs font-medium transition-all whitespace-nowrap ${
                activeTag === tag.id
                  ? 'bg-accent-600 text-white shadow-md'
                  : 'bg-primary-50 text-primary-900 border border-primary-200 hover:bg-primary-100 hover:text-primary-900'
              }`}
              data-tag={tag.id}
            >
              {tag.name}
            </button>
          ))}
        </div>
      </div>
    </div>
  </section>
)}

<style>
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const scrollContainer = document.getElementById('tagScroll');
    const tagTabs = document.querySelectorAll('.tag-tab');

    if (!scrollContainer) return;

    // Enable touch scrolling for mobile devices
    let isDown = false;
    let startX;
    let scrollLeft;

    scrollContainer.addEventListener('touchstart', (e) => {
      isDown = true;
      startX = e.touches[0].pageX - scrollContainer.offsetLeft;
      scrollLeft = scrollContainer.scrollLeft;
    });

    scrollContainer.addEventListener('touchend', () => {
      isDown = false;
    });

      scrollContainer.addEventListener('touchmove', (e) => {
        if (!isDown) return;
        const x = e.touches[0].pageX - scrollContainer.offsetLeft;
        const walk = (x - startX) * 2; // Scroll speed multiplier
        scrollContainer.scrollLeft = scrollLeft - walk;
      });
      
      // Also enable mouse drag scrolling for desktop
      scrollContainer.addEventListener('mousedown', (e) => {
        isDown = true;
        startX = e.pageX - scrollContainer.offsetLeft;
        scrollLeft = scrollContainer.scrollLeft;
        scrollContainer.style.cursor = 'grabbing';
      });

      scrollContainer.addEventListener('mouseleave', () => {
        isDown = false;
        scrollContainer.style.cursor = 'grab';
      });

      scrollContainer.addEventListener('mouseup', () => {
        isDown = false;
        scrollContainer.style.cursor = 'grab';
      });

      scrollContainer.addEventListener('mousemove', (e) => {
        if (!isDown) return;
        e.preventDefault();
        const x = e.pageX - scrollContainer.offsetLeft;
        const walk = (x - startX) * 2; // Scroll speed multiplier
        scrollContainer.scrollLeft = scrollLeft - walk;
      });
      
      // Set initial cursor
      scrollContainer.style.cursor = 'grab';

    // Tag tab click handlers
    tagTabs.forEach(tab => {
      tab.addEventListener('click', (e) => {
        const tagId = e.currentTarget.dataset.tag;

        // Remove active class from all tabs
        tagTabs.forEach(t => {
          t.classList.remove('bg-accent-600', 'text-white', 'shadow-md');
          t.classList.add('bg-primary-50', 'text-primary-900', 'border', 'border-primary-200');
        });

        // Add active class to clicked tab
        e.currentTarget.classList.remove('bg-primary-50', 'text-primary-900', 'border', 'border-primary-200');
        e.currentTarget.classList.add('bg-accent-600', 'text-white', 'shadow-md');

        // Dispatch custom event for filtering
        window.dispatchEvent(new CustomEvent('tagChange', {
          detail: { tagId }
        }));
      });
    });
  });
</script>
