# Middleware Architecture - Unified Cache & Image Optimization

## 📋 **OVERVIEW**

D<PERSON> án sử dụng unified middleware architecture để handle:
- Image optimization & format negotiation
- Comprehensive cache control
- Security & performance headers
- Cloudflare Image Transform integration

## 🏗️ **ARCHITECTURE**

### **Single Middleware File**
```
src/middleware.ts (ONLY middleware file)
```

**Removed:**
- `src/middleware/index.ts` ❌
- `src/middleware/performance.ts` ❌

### **Middleware Flow**

```typescript
export const onRequest = defineMiddleware(async (context, next) => {
  // 1. PRE-PROCESSING: Image format negotiation
  if (url.pathname.startsWith('/cdn-cgi/image/')) {
    // Handle format=auto → format=avif/webp/jpeg
    // Return redirect if needed
  }

  // 2. PROCESS REQUEST
  const response = await next();

  // 3. POST-PROCESSING: Add headers
  // - Cache control based on content type
  // - Security headers
  // - Image optimization headers
  
  return new Response(response.body, { headers });
});
```

## 🎯 **CACHE STRATEGY**

### **Content Type Detection**
```typescript
// Static assets
if (pathname.match(/\.(js|css|woff2?|ttf|eot|svg|ico)$/)) {
  headers.set('Cache-Control', 'public, max-age=31536000, immutable');
}
// Images
else if (pathname.match(/\.(jpg|jpeg|png|webp|avif|gif)$/i)) {
  headers.set('Cache-Control', 'public, max-age=31536000, immutable');
  headers.set('Vary', 'Accept');
}
// API routes
else if (pathname.startsWith('/api/')) {
  headers.set('Cache-Control', 'public, max-age=300, s-maxage=300');
}
// Product pages
else if (pathname.startsWith('/products/')) {
  headers.set('Cache-Control', 'public, max-age=1800, s-maxage=1800');
}
// Other pages
else {
  headers.set('Cache-Control', 'public, max-age=300, s-maxage=300');
}
```

### **Cache Duration Rationale**

| Content Type | Duration | Reason |
|--------------|----------|---------|
| Static Assets | 1 year | Immutable, versioned files |
| Images | 1 year | Rarely change, aggressive cache OK |
| Product Pages | 30 minutes | Semi-static, balance freshness vs performance |
| API Routes | 5 minutes | Dynamic data, need freshness |
| Other Pages | 5 minutes | Homepage/categories, frequent updates |

## 🖼️ **IMAGE OPTIMIZATION**

### **Format Negotiation**
```typescript
// Check Accept header first
if (acceptHeader.includes('image/avif')) {
  optimalFormat = 'avif';
} else if (acceptHeader.includes('image/webp')) {
  optimalFormat = 'webp';
} else {
  optimalFormat = getOptimalFormat(userAgent);
}

// Replace format=auto with optimal format
if (transformParams.includes('format=auto')) {
  const newTransformParams = transformParams.replace('format=auto', `format=${optimalFormat}`);
  return Response.redirect(newUrl.toString(), 302);
}
```

### **Image Headers**
```typescript
// For all images
headers.set('Vary', 'Accept');

// For Cloudflare Image Transform
if (pathname.startsWith('/cdn-cgi/image/')) {
  headers.set('Accept-CH', 'Viewport-Width, Width, DPR');
}
```

## 🔒 **SECURITY HEADERS**

Applied to all responses:
```typescript
headers.set('X-Content-Type-Options', 'nosniff');
headers.set('X-Frame-Options', 'DENY');
headers.set('X-XSS-Protection', '1; mode=block');
headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
```

## ⚡ **PERFORMANCE OPTIMIZATIONS**

### **Resource Preloading**
```typescript
// Homepage font preloading
if (pathname === '/') {
  headers.set('Link', '</fonts/inter.woff2>; rel=preload; as=font; type=font/woff2; crossorigin');
}
```

### **Cloudflare Integration**
- Works seamlessly with Cloudflare Cache Rules
- Edge cache: 30 days for images, 1 year for static assets
- Browser cache: Controlled by middleware headers
- Image Transform: Handled at edge level

## 🧪 **TESTING**

### **Verify Cache Headers**
```bash
# Test homepage
curl -I https://infpik.store/
# Expected: Cache-Control: public, max-age=300, s-maxage=300

# Test product page
curl -I https://infpik.store/products/example
# Expected: Cache-Control: public, max-age=1800, s-maxage=1800

# Test API
curl -I https://infpik.store/api/products
# Expected: Cache-Control: public, max-age=300, s-maxage=300

# Test static asset
curl -I https://infpik.store/_astro/main.css
# Expected: Cache-Control: public, max-age=31536000, immutable
```

### **Verify Image Optimization**
```bash
# Test format negotiation
curl -H "Accept: image/avif,image/webp,*/*" https://infpik.store/cdn-cgi/image/format=auto/example.jpg
# Expected: Redirect to format=avif
```

## 🔧 **MAINTENANCE**

### **Adding New Content Types**
1. Add pattern matching in middleware
2. Set appropriate cache duration
3. Test with curl/DevTools
4. Update documentation

### **Monitoring**
- Cloudflare Analytics → Caching tab
- Check cache hit ratios
- Monitor image resizing usage
- Watch for 404s on image transforms

## 📝 **CHANGELOG**

### **v2.0 - Unified Middleware (Current)**
- ✅ Merged image optimization + performance middleware
- ✅ Fixed HTML page caching issue
- ✅ Comprehensive cache strategy
- ✅ Removed middleware conflicts

### **v1.0 - Separate Middlewares (Deprecated)**
- ❌ `src/middleware.ts` - Image optimization only
- ❌ `src/middleware/performance.ts` - Cache headers only
- ❌ Conflict caused missing HTML cache headers
