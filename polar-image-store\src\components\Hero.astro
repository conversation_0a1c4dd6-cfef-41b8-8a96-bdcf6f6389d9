---
export interface Props {
  title: string;
  subtitle?: string;
}

const {
  title,
  subtitle
} = Astro.props;
---

<section class="relative flex flex-col justify-center items-center py-12 lg:py-20 bg-gradient-to-br from-white via-primary-50/30 to-accent-50/20 overflow-hidden">
  <!-- Background decoration -->
  <div class="absolute inset-0 bg-grid-pattern opacity-5"></div>
  <div class="absolute top-20 right-20 w-72 h-72 bg-accent-200/20 rounded-full blur-3xl"></div>
  <div class="absolute bottom-20 left-20 w-96 h-96 bg-primary-200/20 rounded-full blur-3xl"></div>
  
  <div class="container relative">
    <div class="max-w-4xl mx-auto">
      <!-- 1. Main title -->
      <h1 class="text-3xl md:text-4xl lg:text-5xl xl:text-5xl font-bold text-primary-900 mb-8 leading-tight max-w-3xl mx-auto text-center" set:html={title}>
      </h1>

      <!-- 2. Category Navigation -->
      <div class="relative mb-8">
        <div class="overflow-x-auto scrollbar-hide" id="categoryScroll">
          <div class="flex gap-2 pb-2 min-w-max justify-center">
            <slot name="category-navigation" />
          </div>
        </div>
      </div>

      <!-- 3. Search Bar -->
      <div class="relative mb-8 z-[9999] hero-search-container">
        <div class="max-w-md mx-auto">
          <div class="relative z-[9999]">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="w-5 h-5 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <input
              type="text"
              id="heroSearchInput"
              placeholder="Search for icons..."
              class="block w-full pl-10 pr-4 py-2.5 border border-primary-200 rounded-full bg-white text-primary-900 placeholder-primary-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200 shadow-sm"
              autocomplete="off"
            />
            <!-- Search results dropdown (hidden by default) -->
            <div id="heroSearchResults" class="fixed bg-white border border-primary-200 rounded-xl shadow-2xl z-[9999] hidden max-h-48 overflow-y-auto min-w-[400px]">
              <!-- Search results will be populated here -->
            </div>
          </div>
        </div>
      </div>

      <!-- 4. Tag Navigation -->
      <div class="relative">
        <slot name="tag-navigation" />
      </div>
    </div>
  </div>
</section>

<style>
  .bg-grid-pattern {
    background-image:
      linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Ensure search dropdown is always on top */
  #heroSearchResults {
    z-index: 9999 !important;
    position: fixed !important;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  /* Custom scrollbar for dropdown */
  #heroSearchResults::-webkit-scrollbar {
    width: 8px;
  }

  #heroSearchResults::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
  }

  #heroSearchResults::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
  }

  #heroSearchResults::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.5);
  }

  /* Compact dropdown styling */
  #heroSearchResults {
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.3) rgba(0, 0, 0, 0.05);
  }

  /* Ensure search container has proper stacking context */
  .hero-search-container {
    position: relative;
    z-index: 9999;
    isolation: isolate;
  }

  /* Smooth transitions for dropdown */
  #heroSearchResults {
    transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
  }

  #heroSearchResults.hidden {
    opacity: 0;
    transform: translateY(-10px);
    pointer-events: none;
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const scrollContainer = document.getElementById('categoryScroll');
    
    if (scrollContainer) {
      
      // Enable touch scrolling for mobile devices
      let isDown = false;
      let startX;
      let scrollLeft;

      scrollContainer.addEventListener('touchstart', (e) => {
        isDown = true;
        startX = e.touches[0].pageX - scrollContainer.offsetLeft;
        scrollLeft = scrollContainer.scrollLeft;
      });

      scrollContainer.addEventListener('touchend', () => {
        isDown = false;
      });

      scrollContainer.addEventListener('touchmove', (e) => {
        if (!isDown) return;
        // Không gọi e.preventDefault() để cho phép cuộn mặc định trên thiết bị di động
        const x = e.touches[0].pageX - scrollContainer.offsetLeft;
        const walk = (x - startX) * 2; // Scroll speed multiplier
        scrollContainer.scrollLeft = scrollLeft - walk;
      });
      
      // Also enable mouse drag scrolling for desktop
      scrollContainer.addEventListener('mousedown', (e) => {
        isDown = true;
        startX = e.pageX - scrollContainer.offsetLeft;
        scrollLeft = scrollContainer.scrollLeft;
        scrollContainer.style.cursor = 'grabbing';
      });

      scrollContainer.addEventListener('mouseleave', () => {
        isDown = false;
        scrollContainer.style.cursor = 'grab';
      });

      scrollContainer.addEventListener('mouseup', () => {
        isDown = false;
        scrollContainer.style.cursor = 'grab';
      });

      scrollContainer.addEventListener('mousemove', (e) => {
        if (!isDown) return;
        e.preventDefault();
        const x = e.pageX - scrollContainer.offsetLeft;
        const walk = (x - startX) * 2; // Scroll speed multiplier
        scrollContainer.scrollLeft = scrollLeft - walk;
      });
      
      // Add grab cursor style
      scrollContainer.style.cursor = 'grab';
      
      // Category tab functionality
      const categoryTabs = document.querySelectorAll('.category-tab');
      categoryTabs.forEach(tab => {
        tab.addEventListener('click', (e) => {
          const categoryId = e.currentTarget.dataset.category;

          // Remove active class from all tabs
          categoryTabs.forEach(t => {
            t.classList.remove('bg-accent-500', 'text-white', 'border-accent-500', 'shadow-md');
            t.classList.add('bg-white', 'text-primary-900', 'border-primary-200', 'shadow-sm', 'hover:bg-primary-50', 'hover:border-accent-500');
          });

          // Add active class to clicked tab
          tab.classList.remove('bg-white', 'text-primary-900', 'border-primary-200', 'shadow-sm', 'hover:bg-primary-50', 'hover:border-accent-500');
          tab.classList.add('bg-accent-500', 'text-white', 'border-accent-500', 'shadow-md');

          // Check if we're on homepage or products page
          const isHomepage = window.location.pathname === '/';

          if (isHomepage) {
            // On homepage: dispatch event for client-side filtering
            console.log('📡 Dispatching categoryChange event for homepage:', categoryId);
            window.dispatchEvent(new CustomEvent('categoryChange', {
              detail: { categoryId }
            }));
          } else {
            // On other pages: navigate to category page
            if (categoryId === 'all') {
              window.location.href = '/products';
            } else {
              window.location.href = `/products/category/${categoryId}`;
            }
          }
        });
      });

      // Tag tab functionality
      const tagTabs = document.querySelectorAll('.tag-tab');
      tagTabs.forEach(tab => {
        tab.addEventListener('click', (e) => {
          const tagId = e.currentTarget.dataset.tag;

          // Add visual feedback before navigation
          tagTabs.forEach(t => {
            t.classList.remove('bg-accent-500', 'text-white', 'border-accent-500', 'shadow-md');
            t.classList.add('bg-white', 'text-primary-900', 'border-primary-200', 'shadow-sm', 'hover:bg-primary-50', 'hover:border-accent-500');
          });

          // Add active class to clicked tab
          e.currentTarget.classList.remove('bg-white', 'text-primary-900', 'border-primary-200', 'shadow-sm', 'hover:bg-primary-50', 'hover:border-accent-500');
          e.currentTarget.classList.add('bg-accent-500', 'text-white', 'border-accent-500', 'shadow-md');

          // Navigate to tag page after brief delay for visual feedback
          setTimeout(() => {
            if (tagId === 'all') {
              window.location.href = '/products';
            } else {
              window.location.href = `/products/tag/${tagId}`;
            }
          }, 150);
        });
      });
    }

    // Hero search functionality
    const heroSearchInput = document.getElementById('heroSearchInput');
    const heroSearchResults = document.getElementById('heroSearchResults');
    let searchTimeout;

    // Mobile detection function
    function isMobile() {
      return window.innerWidth < 768;
    }

    // Mobile search modal functionality
    function handleMobileSearchClick(input) {
      if (isMobile()) {
        input.blur(); // Remove focus to prevent keyboard
        // Open search modal instead of redirecting
        const currentQuery = input.value.trim();
        (window as any).openSearchModal?.(currentQuery);
      }
    }

    // Recent search functionality
    function saveRecentSearch(query) {
      try {
        const recent = JSON.parse(localStorage.getItem('recentSearches') || '[]');
        const filtered = recent.filter(item => item !== query);
        filtered.unshift(query);
        const limited = filtered.slice(0, 10);
        localStorage.setItem('recentSearches', JSON.stringify(limited));
      } catch (error) {
        console.error('Failed to save recent search:', error);
      }
    }

    function removeRecentSearch(query) {
      try {
        const recent = JSON.parse(localStorage.getItem('recentSearches') || '[]');
        const filtered = recent.filter(search => search !== query);
        localStorage.setItem('recentSearches', JSON.stringify(filtered));
      } catch (error) {
        console.error('Failed to remove recent search:', error);
      }
    }

    function loadRecentSearches() {
      try {
        const recent = JSON.parse(localStorage.getItem('recentSearches') || '[]');
        if (recent.length > 0 && heroSearchResults) {
          const recentSearchesHtml = `
            <div class="border-b border-primary-100 bg-gray-50 px-3 py-2">
              <div class="flex items-center justify-between">
                <span class="text-xs text-primary-500 font-medium">Recent Searches</span>
                <button id="heroSearchClearRecent" class="text-xs text-primary-400 hover:text-primary-600 transition-colors">
                  Clear
                </button>
              </div>
            </div>
            <div class="p-2">
              ${recent.map((query, index) => `
                <div class="flex items-center justify-between p-2 hover:bg-primary-50 rounded-lg transition-colors ${
                  index < recent.length - 1 ? 'border-b border-primary-100' : ''
                }">
                  <button class="hero-recent-search-item flex-1 text-left" data-query="${query}">
                    <div class="flex items-center gap-3">
                      <div class="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center">
                        <svg class="w-3 h-3 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <span class="text-primary-900 text-sm">${query}</span>
                    </div>
                  </button>
                  <button class="hero-delete-recent-search ml-2 p-1 text-primary-400 hover:text-red-500 transition-colors" data-query="${query}">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              `).join('')}
            </div>

          `;

          heroSearchResults.innerHTML = recentSearchesHtml;
          positionDropdown();
          heroSearchResults.classList.remove('hidden');

          // Add event listeners for recent searches
          heroSearchResults.querySelectorAll('.hero-recent-search-item').forEach(item => {
            item.addEventListener('click', () => {
              const query = item.getAttribute('data-query');
              if (query && heroSearchInput) {
                heroSearchInput.value = query;
                performHeroSearch(query);
              }
            });
          });

          // Add event listeners for delete buttons
          heroSearchResults.querySelectorAll('.hero-delete-recent-search').forEach(button => {
            button.addEventListener('click', (e) => {
              e.stopPropagation();
              const query = button.getAttribute('data-query');
              if (query) {
                removeRecentSearch(query);
                loadRecentSearches(); // Reload the list
              }
            });
          });

          // Add event listener for clear all
          const clearButton = heroSearchResults.querySelector('#heroSearchClearRecent');
          clearButton?.addEventListener('click', () => {
            localStorage.removeItem('recentSearches');
            heroSearchResults?.classList.add('hidden');
          });
        }
      } catch (error) {
        console.error('Failed to load recent searches:', error);
      }
    }

    // Search functionality
    async function performHeroSearch(query) {
      if (!query.trim() || !heroSearchResults) return;

      try {
        heroSearchResults.innerHTML = '<div class="p-4 text-center text-primary-500">Searching...</div>';
        heroSearchResults.classList.remove('hidden');

        const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`);
        const data = await response.json();

        if (data.results && data.results.length > 0) {
          saveRecentSearch(query);
          displayHeroSearchResults(data.results, query);
        } else {
          displayHeroNoResults(query);
        }
      } catch (error) {
        console.error('Search error:', error);
        heroSearchResults.innerHTML = '<div class="p-4 text-center text-red-500">Search failed. Please try again.</div>';
      }
    }

    // Function to position dropdown correctly
    function positionDropdown() {
      if (!heroSearchResults || !heroSearchInput) return;

      const inputRect = heroSearchInput.getBoundingClientRect();
      const maxDropdownHeight = 192; // max-h-48 = 192px
      const viewportHeight = window.innerHeight;
      const spaceBelow = viewportHeight - inputRect.bottom;

      // Position dropdown below input
      heroSearchResults.style.left = `${inputRect.left}px`;
      heroSearchResults.style.width = `${inputRect.width}px`;
      heroSearchResults.style.top = `${inputRect.bottom + 4}px`;
      heroSearchResults.style.bottom = 'auto';

      // Limit height to available space or max height, whichever is smaller
      const availableHeight = Math.min(maxDropdownHeight, spaceBelow - 20);
      heroSearchResults.style.maxHeight = `${Math.max(150, availableHeight)}px`;
    }

    function displayHeroSearchResults(results, query) {
      if (!heroSearchResults) return;

      console.log('Displaying results:', results); // Debug log

      const resultsHtml = results.map(product => `
        <button class="hero-search-result-item w-full p-3 hover:bg-primary-50 transition-colors text-left border-b border-primary-100 last:border-b-0" data-url="${product.url || '#'}">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div class="w-10 h-10 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                ${product.image ? `<img src="${product.image}" alt="${product.name || 'Product'}" class="w-full h-full object-cover">` : `
                  <div class="w-full h-full bg-accent-100 flex items-center justify-center">
                    <svg class="w-5 h-5 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                `}
              </div>
              <div class="flex-1 min-w-0">
                <div class="font-medium text-primary-900 truncate">${product.name || 'Unknown Product'}</div>
                <div class="text-sm text-accent-600 font-medium">$${product.price || '0'} ${product.currency || 'USD'}</div>
              </div>
            </div>
            <svg class="w-4 h-4 text-primary-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </div>
        </button>
      `).join('');

      const searchAllHtml = `
        <div class="border-b border-primary-100 bg-gray-50 px-3 py-2">
          <span class="text-xs text-primary-500 font-medium">Search results for "${query}"</span>
        </div>
        ${resultsHtml}

      `;

      heroSearchResults.innerHTML = searchAllHtml;
      positionDropdown();
      heroSearchResults.classList.remove('hidden');

      // Add click handlers for search results
      heroSearchResults.querySelectorAll('.hero-search-result-item').forEach(item => {
        item.addEventListener('click', () => {
          const url = item.getAttribute('data-url');
          if (url) {
            window.location.href = url;
          }
        });
      });
    }

    function displayHeroNoResults(query) {
      if (!heroSearchResults) return;

      heroSearchResults.innerHTML = `
        <div class="p-4 text-center">
          <div class="text-primary-500 mb-2">No products found for "${query}"</div>
          <a href="/products" class="text-accent-600 hover:text-accent-700 text-sm font-medium">Browse all products →</a>
        </div>
      `;
      positionDropdown();
      heroSearchResults.classList.remove('hidden');
    }

    function handleHeroSearch() {
      const query = heroSearchInput?.value.trim();
      if (!query) {
        loadRecentSearches();
      } else {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
          performHeroSearch(query);
        }, 300);
      }
    }

    if (heroSearchInput) {
      // Handle focus for mobile redirect and show recent searches
      heroSearchInput.addEventListener('focus', (e) => {
        if (isMobile()) {
          handleMobileSearchClick(e.target);
        } else {
          const query = heroSearchInput.value.trim();
          if (!query) {
            loadRecentSearches();
          }
        }
      });

      // Handle click for mobile redirect
      heroSearchInput.addEventListener('click', (e) => {
        if (isMobile()) {
          handleMobileSearchClick(e.target);
        }
      });

      // Handle input changes
      heroSearchInput.addEventListener('input', handleHeroSearch);

      // Handle Enter key
      heroSearchInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
          e.preventDefault();
          const query = heroSearchInput.value.trim();
          if (query) {
            if (isMobile()) {
              (window as any).openSearchModal?.(query);
            } else {
              saveRecentSearch(query);
              window.location.href = `/products?search=${encodeURIComponent(query)}`;
            }
          }
        }
      });
    }

    // Hide search results when clicking outside
    document.addEventListener('click', (e) => {
      if (heroSearchResults && heroSearchInput &&
          !heroSearchResults.contains(e.target) &&
          !heroSearchInput.contains(e.target)) {
        heroSearchResults.classList.add('hidden');
      }
    });

    // Reposition dropdown on window resize
    window.addEventListener('resize', () => {
      if (heroSearchResults && !heroSearchResults.classList.contains('hidden')) {
        positionDropdown();
      }
    });

    // Reposition dropdown on scroll
    window.addEventListener('scroll', () => {
      if (heroSearchResults && !heroSearchResults.classList.contains('hidden')) {
        positionDropdown();
      }
    });
  });
</script>
