{"name": "polar-image-store", "type": "module", "version": "0.0.1", "scripts": {"dev": "wrangler types && astro dev", "build": "wrangler types && astro build", "preview": "wrangler pages dev dist", "preview:local": "astro preview", "astro": "astro", "types": "wrangler types", "deploy": "bun run build && wrangler pages deploy dist", "deploy:staging": "bun run build && wrangler pages deploy dist --env staging", "deploy:production": "bun run build && wrangler pages deploy dist --env production", "test:polar": "node scripts/test-polar.js"}, "dependencies": {"@astrojs/check": "^0.9.4", "@astrojs/cloudflare": "^12.6.0", "@polar-sh/astro": "^0.4.4", "@polar-sh/checkout": "^0.1.11", "@polar-sh/sdk": "^0.34.8", "@tailwindcss/vite": "^4.1.11", "astro": "^5.12.4", "astro-seo": "^0.8.4", "markdown-it": "^14.1.0", "tailwindcss": "^4.1.11", "typescript": "^5.8.3", "zod": "^4.0.11"}, "devDependencies": {"@playform/compress": "^0.2.0", "@types/node": "^24.1.0", "dotenv": "^17.2.1", "@types/markdown-it": "^14.1.2", "wrangler": "^4.26.0"}}