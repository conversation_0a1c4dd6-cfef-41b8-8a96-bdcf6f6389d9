---
// Product FAQ component to address common customer questions
export interface Props {
  class?: string;
}

const { class: className = '' } = Astro.props;

// FAQ data - in a real app, this would come from a CMS or API
const faqs = [
  {
    id: 1,
    question: "What formats are included with my purchase?",
    answer: "You'll receive high-resolution JPEG files optimized for web and print use. Additional formats like PNG may be available depending on the specific image."
  },
  {
    id: 2,
    question: "Can I use these icons for commercial projects?",
    answer: "Yes! All our icons come with a commercial license that allows you to use them in both personal and commercial projects without additional fees."
  },
  {
    id: 3,
    question: "How do I download my icons after purchase?",
    answer: "After completing your purchase, you'll receive an email with download links. You can also access your downloads from your account dashboard."
  },
  {
    id: 4,
    question: "Are there any usage restrictions?",
    answer: "Our license allows broad usage rights. You cannot resell or redistribute the icons as-is, but you can use them in your creative projects and commercial work."
  }
];
---

<div class={`bg-primary-50 rounded-2xl p-6 ${className}`}>
  <h3 class="text-lg font-semibold text-primary-900 mb-6 flex items-center gap-2">
    <svg class="w-5 h-5 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
    </svg>
    Frequently Asked Questions
  </h3>
  
  <div class="space-y-4">
    {faqs.map((faq) => (
      <details class="group bg-white rounded-lg border border-primary-200 overflow-hidden">
        <summary class="flex items-center justify-between p-4 cursor-pointer hover:bg-primary-50 transition-colors">
          <h4 class="font-medium text-primary-900 pr-4">{faq.question}</h4>
          <svg class="w-5 h-5 text-primary-600 transform transition-transform group-open:rotate-180 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </summary>
        <div class="px-4 pb-4">
          <p class="text-primary-700 leading-relaxed">{faq.answer}</p>
        </div>
      </details>
    ))}
  </div>
  
  <div class="mt-6 pt-4 border-t border-primary-200 text-center">
    <p class="text-sm text-primary-600 mb-3">Still have questions?</p>
    <a 
      href="mailto:<EMAIL>" 
      class="inline-flex items-center gap-2 text-accent-600 hover:text-accent-700 font-medium text-sm transition-colors"
    >
      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
      </svg>
      Contact Support
    </a>
  </div>
</div>

<style>
  /* Custom styles for details/summary */
  details[open] summary {
    border-bottom: 1px solid #e5e7eb;
  }
  
  summary::-webkit-details-marker {
    display: none;
  }
  
  summary::marker {
    display: none;
  }
</style>
