---
import '../styles/global.css';
import { Image } from 'astro:assets';
import { SEO } from "astro-seo";
import SearchModal from '../components/SearchModal.astro';

export interface Props {
  title: string;
  description?: string;
  image?: string;
  canonical?: string;
  noindex?: boolean;
  type?: 'website' | 'article' | 'product';
}

const {
  title,
  description = "Premium 3D icons for creative projects (PNG, PSD).",
  image = "/og-image.jpg",
  canonical,
  noindex = false,
  type = 'website'
} = Astro.props;

const siteUrlEnv = import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store';
// Đảm bảo không có tiền tố www trong canonical để tránh trùng lặp
const siteUrl = siteUrlEnv.replace('://www.', '://');
const fullImageUrl = image.startsWith('http') ? image : `${siteUrl}${image}`;
const canonicalUrl = canonical || `${siteUrl}${Astro.url.pathname}`;
---

<!doctype html>
<html lang="en">
  <head>
    <SEO
      title={title}
      description={description}
      canonical={canonicalUrl}
      noindex={noindex}
      charset="UTF-8"
      openGraph={{
        basic: {
          title: title,
          type: type,
          image: fullImageUrl,
          url: canonicalUrl
        },
        optional: {
          description: description,
          siteName: "InfPik",
          locale: "en_US"
        }
      }}
      twitter={{
        card: "summary_large_image",
        site: "@polarimagestore",
        creator: "@polarimagestore",
        title: title,
        description: description,
        image: fullImageUrl,
        imageAlt: `${title} - InfPik`
      }}
      extend={{
        link: [
          { rel: "icon", type: "image/svg+xml", href: "/favicon.svg" },
          { rel: "sitemap", href: "/sitemap.xml" },
          { rel: "manifest", href: "/manifest.json" },
          // Performance optimizations
          { rel: "preconnect", href: "https://fonts.googleapis.com" },
          { rel: "preconnect", href: "https://fonts.gstatic.com", crossorigin: "" },
          { rel: "dns-prefetch", href: "https://polar.sh" },
          { rel: "dns-prefetch", href: "https://amazonaws.com" },
          { rel: "dns-prefetch", href: "https://s3.amazonaws.com" },
          { rel: "preload", href: "/fonts/inter.woff2", as: "font", type: "font/woff2", crossorigin: "" }
        ],
        meta: [
          { name: "viewport", content: "width=device-width, initial-scale=1.0" },
          { name: "generator", content: Astro.generator },
          { name: "robots", content: noindex ? "noindex, nofollow" : "index, follow" },
          { name: "googlebot", content: noindex ? "noindex, nofollow" : "index, follow" },
          { name: "theme-color", content: "#6366f1" },
          { name: "msapplication-TileColor", content: "#6366f1" }
        ]
      }}
    />
  </head>
  <body class="min-h-screen flex flex-col">
    <header class="sticky top-0 z-50 bg-white/95 backdrop-blur-sm border-b border-primary-100 py-4">
      <div class="container">
        <nav class="flex items-center justify-between">
          <!-- Logo -->
          <a href="/" class="flex items-center gap-2 text-xl font-bold text-primary-900 hover:text-accent-600 transition-colors">
            <Image
              src="/logo.svg"
              alt="Logo"
              width={32}
              height={32}
              class="w-8 h-8 text-accent-600"
            />
            <span class="hidden md:inline">InfPik</span>
          </a>

          <!-- Mobile Search Bar -->
          <div id="mobileHeaderSearchContainer" class="md:hidden relative flex-1 mx-2 opacity-0 transform -translate-y-2 transition-all duration-300 ease-in-out">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="w-4 h-4 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <input
              type="text"
              id="mobileProductSearch"
              class="block w-full pl-8 pr-4 py-1.5 border border-primary-200 rounded-full bg-primary-50/50 text-primary-900 placeholder-primary-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200 text-sm"
              placeholder="Search for icons..."
              autocomplete="off"
            />
          </div>

          <!-- Search Bar (Desktop) -->
          <div id="headerSearchContainer" class="hidden md:flex flex-1 max-w-md mx-8 opacity-0 transform -translate-y-2 transition-all duration-300 ease-in-out">
            <div class="relative w-full">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="w-5 h-5 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                id="productSearch"
                class="block w-full pl-10 pr-4 py-2.5 border border-primary-200 rounded-full bg-primary-50/50 text-primary-900 placeholder-primary-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200"
                placeholder="Search for icons..."
                autocomplete="off"
              />
              <!-- Search results dropdown (hidden by default) -->
              <div id="searchResults" class="absolute top-full left-0 right-0 mt-1 bg-white border border-primary-200 rounded-xl shadow-lg z-50 hidden max-h-96 overflow-y-auto">
                <!-- Search results will be populated here -->
              </div>
            </div>
          </div>

          <!-- CTA Button & Mobile Menu -->
          <div class="flex items-center gap-4">
            <a href="/customer-portal" class="btn-secondary hidden md:inline-flex" title="My Orders">
              <img src="/cart.svg" alt="My Orders" class="w-5 h-5" />
            </a>
            <a href="/products" class="btn-primary hidden md:inline-flex">
              Browse Collection
            </a>
            <a href="/trending" class="btn-secondary hidden md:inline-flex" title="Trending">
              <img src="/fire.svg" alt="Trending" class="w-5 h-5" />
            </a>

            <!-- Mobile menu button -->
            <button class="md:hidden p-2 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full transition-all" id="mobile-menu-button">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
              </svg>
            </button>
          </div>
        </nav>

        <!-- Mobile menu -->
        <div class="md:hidden hidden" id="mobile-menu">
          <div class="pt-4 pb-2 border-t border-primary-100 mt-4">
            <!-- Mobile Navigation -->
            <ul class="space-y-2">
              <li><a href="/" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">Home</a></li>
              <li><a href="/products" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">Products</a></li>
              <li><a href="/trending" class="flex items-center gap-2 py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">
                <img src="/fire.svg" alt="Trending" class="w-5 h-5" />
                Trending
              </a></li>
              <li><a href="/customer-portal" class="flex items-center gap-2 py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">
                <img src="/cart.svg" alt="My Orders" class="w-5 h-5" />
                My Orders
              </a></li>
              <li><a href="/about" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">About</a></li>
            </ul>
            
            <!-- Legal Links -->
            <div class="mt-4 pt-4 border-t border-primary-100">
              <p class="px-4 text-xs uppercase text-primary-500 font-medium mb-2">Legal</p>
              <ul class="space-y-2">
                <li><a href="/privacy" class="block py-2 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all text-sm">Privacy Policy</a></li>
                <li><a href="/terms" class="block py-2 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all text-sm">Terms of Service</a></li>
                <li><a href="/license" class="block py-2 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all text-sm">License</a></li>
              </ul>
            </div>
            
            <div class="mt-4 pt-4 border-t border-primary-100">
              <a href="/products" class="btn-primary w-full justify-center">
                Browse Collection
              </a>
              <a href="/trending" class="btn-secondary w-full justify-center mt-2">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 640" class="w-5 h-5 mr-2"><!--!Font Awesome Free v7.0.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.--><path fill="#f66336" d="M256.5 37.6C265.8 29.8 279.5 30.1 288.4 38.5C300.7 50.1 311.7 62.9 322.3 75.9C335.8 92.4 352 114.2 367.6 140.1C372.8 133.3 377.6 127.3 381.8 122.2C382.9 120.9 384 119.5 385.1 118.1C393 108.3 402.8 96 415.9 96C429.3 96 438.7 107.9 446.7 118.1C448 119.8 449.3 121.4 450.6 122.9C460.9 135.3 474.6 153.2 488.3 175.3C515.5 219.2 543.9 281.7 543.9 351.9C543.9 475.6 443.6 575.9 319.9 575.9C196.2 575.9 96 475.7 96 352C96 260.9 137.1 182 176.5 127C196.4 99.3 216.2 77.1 231.1 61.9C239.3 53.5 247.6 45.2 256.6 37.7zM321.7 480C347 480 369.4 473 390.5 459C432.6 429.6 443.9 370.8 418.6 324.6C414.1 315.6 402.6 315 396.1 322.6L370.9 351.9C364.3 359.5 352.4 359.3 346.2 351.4C328.9 329.3 297.1 289 280.9 268.4C275.5 261.5 265.7 260.4 259.4 266.5C241.1 284.3 207.9 323.3 207.9 370.8C207.9 439.4 258.5 480 321.6 480z"/></svg>
                Trending Now
              </a>
            </div>
          </div>
        </div>
      </div>
    </header>

    <main class="flex-1 pb-12">
      <slot />
    </main>

    <footer class="bg-white border-t border-primary-100 py-12 text-primary-600">
      <div class="container">
        <div class="text-center">
          <div class="flex items-center justify-center gap-2 text-lg font-semibold text-primary-900 mb-4">
            <Image 
              src="/logo.svg" 
              alt="Logo" 
              width={24} 
              height={24} 
              class="w-6 h-6 text-accent-600" 
            />
            InfPik
          </div>
          <div class="flex justify-center gap-4 mb-4">
            <a href="/about" class="text-sm hover:text-accent-600 transition-colors">About Us</a>
            <a href="/customer-portal" class="text-sm hover:text-accent-600 transition-colors">Order History</a>
            <a href="/privacy" class="text-sm hover:text-accent-600 transition-colors">Privacy Policy</a>
            <a href="/terms" class="text-sm hover:text-accent-600 transition-colors">Terms of Service</a>
            <a href="/license" class="text-sm hover:text-accent-600 transition-colors">License</a>
          </div>
          <div class="flex justify-center gap-6 mb-4">
            <a href="https://www.behance.net/" target="_blank" rel="noopener noreferrer" class="text-primary-600 hover:text-accent-600 transition-colors">
              <Image
                src="/behance.svg"
                alt="Behance"
                width={24}
                height={24}
                class="w-6 h-6"
              />
            </a>
            <a href="https://www.instagram.com/" target="_blank" rel="noopener noreferrer" class="text-primary-600 hover:text-accent-600 transition-colors">
               <Image
                 src="/instagram.svg"
                 alt="Instagram"
                 width={24}
                 height={24}
                 class="w-6 h-6"
               />
             </a>
            <a href="https://www.facebook.com/" target="_blank" rel="noopener noreferrer" class="text-primary-600 hover:text-accent-600 transition-colors">
               <Image
                 src="/facebook.svg"
                 alt="Facebook"
                 width={24}
                 height={24}
                 class="w-6 h-6"
               />
             </a>
            <a href="https://www.pinterest.com/" target="_blank" rel="noopener noreferrer" class="text-primary-600 hover:text-accent-600 transition-colors">
               <Image
                 src="/pinterest.svg"
                 alt="Pinterest"
                 width={24}
                 height={24}
                 class="w-6 h-6"
               />
             </a>
            <a href="https://dribbble.com/" target="_blank" rel="noopener noreferrer" class="text-primary-600 hover:text-accent-600 transition-colors">
               <Image
                 src="/dribbble.svg"
                 alt="Dribbble"
                 width={24}
                 height={24}
                 class="w-6 h-6"
               />
             </a>
          </div>
          <p class="text-sm">&copy; 2025 InfPik. All rights reserved.</p>
        </div>
      </div>
    </footer>

    <!-- Search Modal for Mobile -->
    <SearchModal />
  </body>
</html>

<script>
  // Mobile menu toggle
  document.addEventListener('DOMContentLoaded', () => {
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');

    if (mobileMenuButton && mobileMenu) {
      mobileMenuButton.addEventListener('click', () => {
        mobileMenu.classList.toggle('hidden');
      });
    }

    // Header search bar visibility based on Hero search bar position
    const headerSearchContainer = document.getElementById('headerSearchContainer');
    const mobileHeaderSearchContainer = document.getElementById('mobileHeaderSearchContainer');

    function updateHeaderSearchVisibility(isHeroSearchVisible) {
      // Handle desktop search bar
      if (headerSearchContainer) {
        if (isHeroSearchVisible) {
          // Hero search is visible - hide header search bar
          headerSearchContainer.classList.add('opacity-0', '-translate-y-2');
          headerSearchContainer.classList.remove('opacity-100', 'translate-y-0');
        } else {
          // Hero search is not visible - show header search bar
          headerSearchContainer.classList.remove('opacity-0', '-translate-y-2');
          headerSearchContainer.classList.add('opacity-100', 'translate-y-0');
        }
      }

      // Handle mobile search bar
      if (mobileHeaderSearchContainer) {
        if (isHeroSearchVisible) {
          // Hero search is visible - hide mobile header search bar
          mobileHeaderSearchContainer.classList.add('opacity-0', '-translate-y-2');
          mobileHeaderSearchContainer.classList.remove('opacity-100', 'translate-y-0');
        } else {
          // Hero search is not visible - show mobile header search bar
          mobileHeaderSearchContainer.classList.remove('opacity-0', '-translate-y-2');
          mobileHeaderSearchContainer.classList.add('opacity-100', 'translate-y-0');
        }
      }
    }

    // Use Intersection Observer to detect when Hero search bar is visible
    function setupHeroSearchObserver() {
      const heroSearchContainer = document.querySelector('.hero-search-container');

      if (!heroSearchContainer) {
        // If no hero search container found (e.g., not on homepage), show header search bars
        updateHeaderSearchVisibility(false);
        return;
      }

      // Initially hide header search bars since hero search is visible on page load
      updateHeaderSearchVisibility(true);

      // Create intersection observer with threshold to detect when hero search is completely out of view
      const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          // entry.isIntersecting is true when hero search is visible in viewport
          updateHeaderSearchVisibility(entry.isIntersecting);
        });
      }, {
        // Use root margin to trigger slightly before/after the element enters/leaves viewport
        rootMargin: '0px 0px -10px 0px',
        threshold: 0
      });

      observer.observe(heroSearchContainer);
    }

    // Setup observer on page load
    setupHeroSearchObserver();

    // Search functionality
    const searchInput = document.getElementById('productSearch');
    const mobileSearchInput = document.getElementById('mobileProductSearch');
    const searchResults = document.getElementById('searchResults');

    let searchTimeout: any;

    // Mobile detection function
    function isMobile() {
      return window.innerWidth < 768;
    }

    // Mobile search modal functionality
    function handleMobileSearchClick(input: HTMLInputElement) {
      if (isMobile()) {
        input.blur(); // Remove focus to prevent keyboard
        // Open search modal instead of redirecting
        const currentQuery = input.value.trim();
        (window as any).openSearchModal?.(currentQuery);
      }
    }

    // Recent search functionality (shared with mobile modal)
    function saveRecentSearch(query: string) {
      try {
        const recent = JSON.parse(localStorage.getItem('recentSearches') || '[]');
        const filtered = recent.filter((item: string) => item !== query);
        filtered.unshift(query);
        const limited = filtered.slice(0, 10);
        localStorage.setItem('recentSearches', JSON.stringify(limited));
      } catch (error) {
        console.error('Failed to save recent search:', error);
      }
    }

    function loadRecentSearches() {
      try {
        const recent = JSON.parse(localStorage.getItem('recentSearches') || '[]');
        if (recent.length > 0 && searchResults) {
          const recentSearchesHtml = `
            <div class="border-b border-primary-100 bg-gray-50 px-3 py-2">
              <div class="flex items-center justify-between">
                <span class="text-xs text-primary-500 font-medium">Recent Searches</span>
                <button id="clearRecentSearches" class="text-xs text-primary-400 hover:text-primary-600 transition-colors">
                  Clear
                </button>
              </div>
            </div>
            <div class="p-2">
              ${recent.map((query: string, index: number) => `
                <div class="flex items-center justify-between p-2 hover:bg-primary-50 rounded-lg transition-colors ${
                  index < recent.length - 1 ? 'border-b border-primary-100' : ''
                }">
                  <button class="recent-search-item flex-1 text-left" data-query="${query}">
                    <div class="flex items-center gap-3">
                      <div class="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center">
                        <svg class="w-3 h-3 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <span class="text-primary-900 text-sm">${query}</span>
                    </div>
                  </button>
                  <button class="delete-recent-search ml-2 p-1 text-primary-400 hover:text-red-500 transition-colors" data-query="${query}">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              `).join('')}
            </div>
          `;

          searchResults.innerHTML = recentSearchesHtml;
          searchResults.classList.remove('hidden');

          // Add event listeners for recent searches
          searchResults.querySelectorAll('.recent-search-item').forEach(item => {
            item.addEventListener('click', () => {
              const query = item.getAttribute('data-query');
              if (query && searchInput) {
                searchInput.value = query;
                performSearchWithQuery(query);
              }
            });
          });

          // Add event listeners for delete buttons
          searchResults.querySelectorAll('.delete-recent-search').forEach(button => {
            button.addEventListener('click', (e) => {
              e.stopPropagation();
              const query = button.getAttribute('data-query');
              if (query) {
                removeRecentSearch(query);
                loadRecentSearches(); // Reload the list
              }
            });
          });

          // Add event listener for clear all
          const clearButton = searchResults.querySelector('#clearRecentSearches');
          clearButton?.addEventListener('click', () => {
            localStorage.removeItem('recentSearches');
            searchResults?.classList.add('hidden');
          });
        }
      } catch (error) {
        console.error('Failed to load recent searches:', error);
      }
    }

    function removeRecentSearch(queryToRemove: string) {
      try {
        const recent = JSON.parse(localStorage.getItem('recentSearches') || '[]');
        const filtered = recent.filter((item: string) => item !== queryToRemove);
        localStorage.setItem('recentSearches', JSON.stringify(filtered));
      } catch (error) {
        console.error('Failed to remove recent search:', error);
      }
    }

    function performSearchWithQuery(query: string) {
      saveRecentSearch(query);
      handleSearch({ value: query });
    }

    // Real search functionality with API
    async function handleSearch(input: any) {
      const query = input.value.trim();

      // Clear previous timeout
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }

      if (query.length > 2) {
        // Show search results dropdown with loading
        if (searchResults) {
          searchResults.classList.remove('hidden');
          searchResults.innerHTML = `
            <div class="p-4 text-center text-primary-600">
              <div class="flex items-center justify-center gap-2">
                <img src="/logo.svg" alt="InfPik" class="w-4 h-4 animate-pulse" />
                <span class="text-sm">Searching products for "${query}"...</span>
              </div>
            </div>
          `;

          // Debounce search API call for products
          searchTimeout = setTimeout(async () => {
            try {
              const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`);
              const data = await response.json();

              if (searchResults && !searchResults.classList.contains('hidden')) {
                if (data.results && data.results.length > 0) {
                  const resultsHtml = data.results.map((product: any) => `
                    <button onclick="window.location.href='${product.url || '#'}'" class="block w-full p-3 hover:bg-primary-50 rounded-lg transition-colors border-b border-primary-100 last:border-b-0 text-left">
                      <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                          <div class="w-10 h-10 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                            ${product.image ? `<img src="${product.image}" alt="${product.name || 'Product'}" class="w-full h-full object-cover">` : `
                              <div class="w-full h-full bg-accent-100 flex items-center justify-center">
                                <svg class="w-5 h-5 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                              </div>
                            `}
                          </div>
                          <div class="flex-1 min-w-0">
                            <div class="text-primary-900 font-medium truncate">${product.name || 'Unknown Product'}</div>
                            <div class="text-sm text-accent-600 font-medium">$${product.price || '0'} ${product.currency || 'USD'}</div>
                          </div>
                        </div>
                        <svg class="w-4 h-4 text-primary-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                    </button>
                  `).join('');

                  searchResults.innerHTML = `
                    <div class="p-2">
                      <div class="text-xs text-primary-500 px-3 py-2 border-b border-primary-100">
                        Search results for "${query}"
                      </div>
                      ${resultsHtml}
                      ${data.total > data.results.length ? `
                        <div class="p-3 border-t border-primary-100">
                          <div class="text-center text-primary-600 text-sm">
                            Showing ${data.results.length} of ${data.total} products
                          </div>
                        </div>
                      ` : ''}
                    </div>
                  `;
                } else {
                  searchResults.innerHTML = `
                    <div class="p-4 text-center">
                      <div class="text-primary-600 mb-2">No products found for "${query}"</div>
                      <a href="/products" class="text-accent-600 hover:text-accent-700 font-medium text-sm transition-colors">
                        Browse all products →
                      </a>
                    </div>
                  `;
                }
              }
            } catch (error) {
              console.error('Tag search error:', error);
              if (searchResults && !searchResults.classList.contains('hidden')) {
                searchResults.innerHTML = `
                  <div class="p-4 text-center text-red-600">
                    <div class="text-sm">Search failed. Please try again.</div>
                  </div>
                `;
              }
            }
          }, 300); // 300ms debounce
        }
      } else {
        // Show recent searches when input is empty
        loadRecentSearches();
      }
    }

    // Add search event listeners
    if (searchInput) {
      // Handle focus for mobile redirect
      searchInput.addEventListener('focus', (e: any) => {
        handleMobileSearchClick(e.target);
      });

      // Handle click for mobile redirect
      searchInput.addEventListener('click', (e: any) => {
        handleMobileSearchClick(e.target);
      });

      // Desktop search functionality
      searchInput.addEventListener('input', (e: any) => {
        if (!isMobile()) {
          handleSearch(e.target);
        }
      });

      // Show recent searches on focus (desktop only)
      searchInput.addEventListener('focus', (e: any) => {
        if (!isMobile() && !e.target.value.trim()) {
          loadRecentSearches();
        }
      });

      searchInput.addEventListener('keydown', (e: any) => {
        if (e.key === 'Enter') {
          e.preventDefault();
          const query = e.target.value.trim();
          if (query) {
            if (isMobile()) {
              // Open search modal with query instead of redirecting
              (window as any).openSearchModal?.(query);
            } else {
              // Save to recent searches before navigating
              saveRecentSearch(query);
              window.location.href = `/products?search=${encodeURIComponent(query)}`;
            }
          }
        }
      });
    }

    if (mobileSearchInput) {
      // Handle focus for mobile redirect
      mobileSearchInput.addEventListener('focus', (e: any) => {
        handleMobileSearchClick(e.target);
      });

      // Handle click for mobile redirect
      mobileSearchInput.addEventListener('click', (e: any) => {
        handleMobileSearchClick(e.target);
      });

      mobileSearchInput.addEventListener('keydown', (e: any) => {
        if (e.key === 'Enter') {
          e.preventDefault();
          const query = e.target.value.trim();
          if (query) {
            // Open search modal with query instead of redirecting
            (window as any).openSearchModal?.(query);
          }
        }
      });
    }

    // Hide search results when clicking outside
    document.addEventListener('click', (e: any) => {
      if (searchResults && !searchInput?.contains(e.target as Node) && !searchResults.contains(e.target as Node)) {
        searchResults.classList.add('hidden');
      }
    });
  });

  // Service Worker registration for performance optimization
  if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
      navigator.serviceWorker.register('/sw.js')
        .then((registration) => {
          console.log('SW registered: ', registration);
        })
        .catch((registrationError) => {
          console.log('SW registration failed: ', registrationError);
        });
    });
  }
</script>
