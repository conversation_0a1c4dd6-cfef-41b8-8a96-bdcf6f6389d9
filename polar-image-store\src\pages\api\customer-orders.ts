import type { APIRoute } from 'astro';
import { createPolarClient } from '../../utils/polar';

export const prerender = false;

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    const body = await request.json().catch(() => ({} as any));
    const email = typeof (body as any)?.email === 'string' ? (body as any).email : '';

    if (!email || !email.trim()) {
      return new Response(
        JSON.stringify({ error: 'Email is required' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Get runtime environment from Cloudflare context
    const env = locals?.runtime?.env;
    const polar = createPolarClient(env);
    const organizationId = env?.POLAR_ORGANIZATION_ID || import.meta.env.POLAR_ORGANIZATION_ID;
    
    if (!organizationId) {
      return new Response(
        JSON.stringify({ error: 'Organization ID not configured' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Normalize email (Polar filters are exact match on email)
    const emailNormalized = email.trim().toLowerCase();

    // Try 1: Exact email match within organization
    let customerItems: any[] = [];
    try {
      const byEmail = await polar.customers.list({
        email: emailNormalized,
        organizationId
      });
      customerItems = (byEmail as any)?.items ?? (byEmail as any)?.result?.items ?? [];
    } catch (e) {
      console.warn('customers.list exact email failed', e);
    }

    // Try 2: Query search by email (fallback, partial-insensitive)
    if (!customerItems?.length) {
      try {
        const byQuery = await polar.customers.list({
          query: emailNormalized,
          organizationId
        });
        customerItems = (byQuery as any)?.items ?? (byQuery as any)?.result?.items ?? [];
      } catch (e) {
        console.warn('customers.list query fallback failed', e);
      }
    }

    // Pick exact-match customer by email (case-insensitive)
    const customer = (customerItems || []).find((c: any) => (c?.email || '').toLowerCase() === emailNormalized) || null;

    // If still not found, degrade gracefully: return 200 with empty orders
    if (!customer?.id) {
      return new Response(
        JSON.stringify({
          customer: { id: null, email: emailNormalized, name: null, createdAt: null },
          orders: [],
          benefits: [],
          totalOrders: 0,
          notFound: true
        }),
        { status: 200, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Fetch organization orders then filter by customer (SDK orders.list may not support customerId filter)
    let orders: any[] = [];
    try {
      const ordersResponse = await polar.orders.list({
        organizationId,
        limit: 100
      } as any);
      const rawOrders = (ordersResponse as any)?.items ?? (ordersResponse as any)?.result?.items ?? [];
      // Keep only the customer's paid orders
      orders = rawOrders.filter((o: any) => (o?.customer?.id === customer.id) && ((o?.status || '').toLowerCase() === 'paid'));
    } catch (error) {
      console.warn('Failed to fetch orders:', error);
      orders = [];
    }

    // Create pre-authenticated customer session to access portal endpoints (downloadables)
    let benefits: any[] = [];
    try {
      const session = await polar.customerSessions.create({ customerId: customer.id });
      if (session?.token) {
        const apiBase = 'https://api.polar.sh';
        const url = `${apiBase}/v1/customer-portal/downloadables?organization_id=${encodeURIComponent(organizationId)}&limit=100`;
        const res = await fetch(url, {
          method: 'GET',
          headers: {
            'Customer-Session': session.token,
            'Authorization': `Bearer ${session.token}`
          }
        });
        if (res.ok) {
          const json: any = await res.json();
          const items = json?.items ?? json?.result?.items ?? [];
          benefits = items.map((it: any) => ({
            id: it.id,
            benefitId: it.benefit_id || it.benefitId,
            file: {
              id: it.file?.id,
              name: it.file?.name,
              size: it.file?.size,
              mimeType: it.file?.mime_type || it.file?.mimeType,
              download: it.file?.download,
              url: it.file?.download?.url
            }
          }));
        } else {
          console.warn('Downloadables HTTP error', res.status);
        }
      }
    } catch (err) {
      console.warn('Failed to fetch downloadables:', err);
      benefits = [];
    }

    // Transform orders to include product information
    const transformedOrders = orders.map(order => ({
      id: order.id,
      amount: order.amount,
      taxAmount: order.taxAmount,
      currency: order.currency,
      createdAt: order.createdAt,
      status: order.status,
      products: order.products || [],
      checkoutId: order.checkoutId,
      customFieldValue: order.customFieldValue || {}
    }));

    return new Response(
      JSON.stringify({
        customer: {
          id: customer.id,
          email: customer.email,
          name: customer.name,
          createdAt: customer.createdAt
        },
        orders: transformedOrders,
        benefits: benefits,
        totalOrders: orders.length
      }),
      { 
        status: 200, 
        headers: { 'Content-Type': 'application/json' } 
      }
    );
    
  } catch (error) {
    console.error('Customer orders API error:', error);
    return new Response(
      JSON.stringify({ error: 'Failed to fetch customer orders' }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
};
