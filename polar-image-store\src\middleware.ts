/**
 * Unified middleware for image optimization, format negotiation, and performance headers
 * Combines image optimization logic with comprehensive cache control and security headers
 */

import { defineMiddleware } from 'astro:middleware';
import { getOptimalFormat } from './utils/imageOptimization';

// Generate ETag based on content and timestamp
function generateETag(content: string, lastModified: Date): string {
  const hash = btoa(content.slice(0, 100) + lastModified.getTime().toString()).slice(0, 16);
  return `"${hash}"`;
}

// Get cache duration based on content type
function getCacheDuration(pathname: string): { maxAge: number; sMaxAge: number } {
  if (pathname.match(/\.(js|css|woff2?|ttf|eot|svg|ico)$/)) {
    return { maxAge: 31536000, sMaxAge: 31536000 }; // 1 year
  } else if (pathname.match(/\.(jpg|jpeg|png|webp|avif|gif)$/i)) {
    return { maxAge: 31536000, sMaxAge: 31536000 }; // 1 year
  } else if (pathname.startsWith('/api/')) {
    return { maxAge: 300, sMaxAge: 300 }; // 5 minutes
  } else if (pathname.startsWith('/products/')) {
    return { maxAge: 1800, sMaxAge: 1800 }; // 30 minutes
  } else {
    return { maxAge: 300, sMaxAge: 300 }; // 5 minutes
  }
}

export const onRequest = defineMiddleware(async (context, next) => {
  const { request, url } = context;

  // Handle conditional requests (If-None-Match, If-Modified-Since)
  const ifNoneMatch = request.headers.get('if-none-match');
  const ifModifiedSince = request.headers.get('if-modified-since');

  // Handle image requests with format negotiation BEFORE processing
  if (url.pathname.startsWith('/cdn-cgi/image/')) {
    const userAgent = request.headers.get('user-agent') || '';
    const acceptHeader = request.headers.get('accept') || '';

    // Determine optimal format based on browser support
    let optimalFormat: string;
    if (acceptHeader.includes('image/avif')) {
      optimalFormat = 'avif';
    } else if (acceptHeader.includes('image/webp')) {
      optimalFormat = 'webp';
    } else {
      optimalFormat = getOptimalFormat(userAgent);
    }

    // Parse the current URL to modify format if needed
    const pathParts = url.pathname.split('/');
    if (pathParts.length >= 4) {
      const transformParams = pathParts[3];
      const imageUrl = pathParts.slice(4).join('/');

      // Check if format=auto is in the params
      if (transformParams.includes('format=auto')) {
        const newTransformParams = transformParams.replace('format=auto', `format=${optimalFormat}`);
        const newPath = `/cdn-cgi/image/${newTransformParams}/${imageUrl}`;

        // Create new URL with optimized format
        const newUrl = new URL(newPath, url.origin);
        return Response.redirect(newUrl.toString(), 302);
      }
    }
  }

  // Process the request
  const response = await next();

  // Create new headers object to modify response headers
  const headers = new Headers(response.headers);
  const pathname = url.pathname;
  const now = new Date();

  // Get response body for ETag generation (for HTML pages only)
  let responseBody = '';
  let shouldGenerateETag = false;

  // Only generate ETag for HTML pages (not static assets)
  if (!pathname.match(/\.(js|css|woff2?|ttf|eot|svg|ico|jpg|jpeg|png|webp|avif|gif)$/i)) {
    shouldGenerateETag = true;
    try {
      responseBody = await response.text();
    } catch (e) {
      // If we can't read the body, skip ETag generation
      shouldGenerateETag = false;
    }
  }

  // ===== CACHE CONTROL STRATEGY =====

  // Override TTL for /trending to 15 minutes to reduce API calls
  let { maxAge, sMaxAge } = getCacheDuration(pathname);
  if (pathname === '/trending') {
    maxAge = 900; sMaxAge = 900; // 15 minutes
  }

  // 1. Static assets (CSS, JS, fonts, icons) - Aggressive cache
  if (pathname.match(/\.(js|css|woff2?|ttf|eot|svg|ico)$/)) {
    headers.set('Cache-Control', `public, max-age=${maxAge}, immutable`);
    headers.set('Last-Modified', now.toUTCString());
  }
  // 2. Images (including transformed images) - Aggressive cache with format negotiation
  else if (pathname.match(/\.(jpg|jpeg|png|webp|avif|gif)$/i)) {
    headers.set('Cache-Control', `public, max-age=${maxAge}, immutable`);
    headers.set('Vary', 'Accept');
    headers.set('Last-Modified', now.toUTCString());
  }
  // 3. API routes - Short cache for dynamic data
  else if (pathname.startsWith('/api/')) {
    // Never cache POST/PUT/PATCH/DELETE API calls
    const isMutation = request.method !== 'GET' && request.method !== 'HEAD';
    if (isMutation) {
      headers.set('Cache-Control', 'no-store');
      // For safety, strip any validator headers
      headers.delete('ETag');
      headers.delete('Last-Modified');
      // Skip 304 logic for mutations
    } else {
      headers.set('Cache-Control', `public, max-age=${maxAge}, s-maxage=${sMaxAge}, must-revalidate`);
      headers.set('Last-Modified', now.toUTCString());

      if (shouldGenerateETag && responseBody) {
        const etag = generateETag(responseBody, now);
        headers.set('ETag', etag);

        // Check if client has fresh version
        if (ifNoneMatch === etag) {
          return new Response(null, { status: 304, headers });
        }
      }
    }
  }
  // 4. Product pages - Medium cache for semi-static content
  else if (pathname.startsWith('/products/')) {
    headers.set('Cache-Control', `public, max-age=${maxAge}, s-maxage=${sMaxAge}, must-revalidate`);
    headers.set('Last-Modified', now.toUTCString());

    if (shouldGenerateETag && responseBody) {
      const etag = generateETag(responseBody, now);
      headers.set('ETag', etag);

      // Check if client has fresh version
      if (ifNoneMatch === etag) {
        return new Response(null, { status: 304, headers });
      }
    }
  }
  // 5. Other pages (homepage, category pages, etc.) - Short cache for fresh content
  else {
    headers.set('Cache-Control', `public, max-age=${maxAge}, s-maxage=${sMaxAge}, must-revalidate`);
    headers.set('Last-Modified', now.toUTCString());

    if (shouldGenerateETag && responseBody) {
      const etag = generateETag(responseBody, now);
      headers.set('ETag', etag);

      // Check if client has fresh version
      if (ifNoneMatch === etag) {
        return new Response(null, { status: 304, headers });
      }
    }
  }

  // ===== IMAGE OPTIMIZATION HEADERS =====

  // Add AVIF/WebP support headers for Cloudflare Image Transform
  if (pathname.startsWith('/cdn-cgi/image/')) {
    headers.set('Vary', 'Accept');
    headers.set('Accept-CH', 'Viewport-Width, Width, DPR');
  }

  // ===== SECURITY & PERFORMANCE HEADERS =====

  headers.set('X-Content-Type-Options', 'nosniff');
  headers.set('X-Frame-Options', 'DENY');
  headers.set('X-XSS-Protection', '1; mode=block');
  headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');

  // ===== RESOURCE PRELOADING =====

  // Preload critical resources for homepage
  if (pathname === '/') {
    headers.set('Link', '</fonts/inter.woff2>; rel=preload; as=font; type=font/woff2; crossorigin');
  }

  // Return response with updated headers
  if (shouldGenerateETag && responseBody) {
    // Return response with modified body (for HTML pages with ETag)
    return new Response(responseBody, {
      status: response.status,
      statusText: response.statusText,
      headers,
    });
  } else {
    // Return original response with updated headers (for static assets)
    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers,
    });
  }
});
