// Service Worker for smart caching and performance optimization
const CACHE_VERSION = 'v2'; // Increment to force cache refresh
const CACHE_NAME = `infpik-dynamic-${CACHE_VERSION}`;
const STATIC_CACHE_NAME = `infpik-static-${CACHE_VERSION}`;

// Cache durations (in milliseconds)
const CACHE_DURATIONS = {
  HTML_PAGES: 5 * 60 * 1000,      // 5 minutes for HTML pages
  PRODUCT_PAGES: 30 * 60 * 1000,  // 30 minutes for product pages
  API_ROUTES: 5 * 60 * 1000,      // 5 minutes for API routes
  STATIC_ASSETS: 24 * 60 * 60 * 1000, // 24 hours for static assets
  IMAGES: 7 * 24 * 60 * 60 * 1000     // 7 days for images
};

// Resources to cache immediately (critical resources only)
const STATIC_RESOURCES = [
  '/fonts/inter.woff2',
  '/logo.svg',
  '/favicon.svg',
  '/manifest.json'
];

// Helper function to check if cached response is still fresh
function isCacheFresh(cachedResponse, maxAge) {
  if (!cachedResponse) return false;

  const cachedDate = cachedResponse.headers.get('sw-cached-date');
  if (!cachedDate) return false;

  const cacheTime = new Date(cachedDate).getTime();
  const now = Date.now();

  return (now - cacheTime) < maxAge;
}

// Helper function to add timestamp to response
function addCacheTimestamp(response) {
  const responseClone = response.clone();
  const headers = new Headers(responseClone.headers);
  headers.set('sw-cached-date', new Date().toISOString());

  return new Response(responseClone.body, {
    status: responseClone.status,
    statusText: responseClone.statusText,
    headers: headers
  });
}

// Install event - cache critical static resources only
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then((cache) => {
        return cache.addAll(STATIC_RESOURCES);
      })
      .then(() => {
        return self.skipWaiting();
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            // Delete old cache versions
            if (!cacheName.includes(CACHE_VERSION)) {
              console.log('Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        return self.clients.claim();
      })
  );
});

// Fetch event - smart caching strategy
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }

  // Skip external requests
  if (url.origin !== self.location.origin) {
    return;
  }

  // Determine cache strategy based on URL pattern
  const pathname = url.pathname;
  let strategy = 'network-first'; // Default strategy
  let maxAge = CACHE_DURATIONS.HTML_PAGES;
  let cacheName = CACHE_NAME;

  // Static assets - Cache first with long TTL
  if (pathname.match(/\.(js|css|woff2?|ttf|eot|svg|ico)$/)) {
    strategy = 'cache-first';
    maxAge = CACHE_DURATIONS.STATIC_ASSETS;
    cacheName = STATIC_CACHE_NAME;
  }
  // Images - Cache first with medium TTL
  else if (pathname.match(/\.(png|jpg|jpeg|webp|avif|gif)$/)) {
    strategy = 'cache-first';
    maxAge = CACHE_DURATIONS.IMAGES;
    cacheName = STATIC_CACHE_NAME;
  }
  // Product pages - Network first with medium TTL
  else if (pathname.startsWith('/products/')) {
    strategy = 'network-first';
    maxAge = CACHE_DURATIONS.PRODUCT_PAGES;
  }
  // API routes - Network first with short TTL
  else if (pathname.startsWith('/api/')) {
    strategy = 'network-first';
    maxAge = CACHE_DURATIONS.API_ROUTES;
  }
  // Homepage and other pages - Network first with short TTL
  else {
    strategy = 'network-first';
    maxAge = CACHE_DURATIONS.HTML_PAGES;
  }

  if (strategy === 'cache-first') {
    event.respondWith(cacheFirstStrategy(request, cacheName, maxAge));
  } else {
    event.respondWith(networkFirstStrategy(request, cacheName, maxAge));
  }
});

// Cache-first strategy for static assets
async function cacheFirstStrategy(request, cacheName, maxAge) {
  try {
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);

    // Return cached version if fresh
    if (cachedResponse && isCacheFresh(cachedResponse, maxAge)) {
      return cachedResponse;
    }

    // Fetch fresh version
    const networkResponse = await fetch(request);

    if (networkResponse && networkResponse.status === 200) {
      const responseToCache = addCacheTimestamp(networkResponse);
      cache.put(request, responseToCache.clone());
      return responseToCache;
    }

    // Fallback to stale cache if network fails
    return cachedResponse || networkResponse;
  } catch (error) {
    console.error('Cache-first strategy failed:', error);
    return fetch(request);
  }
}

// Network-first strategy for dynamic content
async function networkFirstStrategy(request, cacheName, maxAge) {
  try {
    // Try network first
    const networkResponse = await fetch(request);

    if (networkResponse && networkResponse.status === 200) {
      const cache = await caches.open(cacheName);
      const responseToCache = addCacheTimestamp(networkResponse);
      cache.put(request, responseToCache.clone());
      return responseToCache;
    }

    // Fallback to cache if network fails
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    return cachedResponse || networkResponse;
  } catch (error) {
    // Network failed, try cache
    console.log('Network failed, trying cache for:', request.url);
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);

    if (cachedResponse && isCacheFresh(cachedResponse, maxAge)) {
      return cachedResponse;
    }

    // Return stale cache or throw error
    return cachedResponse || new Response('Network error', { status: 503 });
  }
}
